
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import InventoryItem, InventoryCategory, InventoryUnit, InventoryTransaction, StockAdjustment, StockTransfer, StockTransferItem, InventoryLocation
from decimal import Decimal
from datetime import datetime

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
def inventory():
    # الحصول على جميع أصناف المخزون
    items = InventoryItem.query.all()
    return render_template('inventory/inventory.html', items=items)

@inventory_bp.route('/items')
@login_required
def items():
    # الحصول على جميع أصناف المخزون
    items = InventoryItem.query.all()
    return render_template('inventory/items.html', items=items)

@inventory_bp.route('/items/new', methods=['GET', 'POST'])
@login_required
def new_item():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        code = request.form.get('code')
        name = request.form.get('name')
        description = request.form.get('description')
        category_id = request.form.get('category_id')
        unit_id = request.form.get('unit_id')
        opening_quantity = Decimal(request.form.get('opening_quantity', 0))
        opening_value = Decimal(request.form.get('opening_value', 0))
        reorder_level = Decimal(request.form.get('reorder_level', 0))
        reorder_quantity = Decimal(request.form.get('reorder_quantity', 0))
        unit_cost = Decimal(request.form.get('unit_cost', 0))
        selling_price = Decimal(request.form.get('selling_price', 0))
        tax_rate = Decimal(request.form.get('tax_rate', 0))

        # التحقق من عدم تكرار كود الصنف
        existing_item = InventoryItem.query.filter_by(code=code).first()
        if existing_item:
            flash('كود الصنف موجود بالفعل', 'danger')
            return render_template('inventory/new_item.html')

        # إنشاء الصنف الجديد
        new_item = InventoryItem(
            code=code,
            name=name,
            description=description,
            category_id=category_id,
            unit_id=unit_id,
            opening_quantity=opening_quantity,
            opening_value=opening_value,
            current_quantity=opening_quantity,
            reorder_level=reorder_level,
            reorder_quantity=reorder_quantity,
            unit_cost=unit_cost,
            selling_price=selling_price,
            tax_rate=tax_rate
        )

        db.session.add(new_item)
        db.session.commit()

        # تسجيل حركة المخزون الافتتاحية
        if opening_quantity > 0:
            transaction = InventoryTransaction(
                item_id=new_item.id,
                transaction_type='opening',
                quantity=opening_quantity,
                unit_cost=unit_cost,
                value=opening_value,
                reference_id='OPENING',
                notes='حركة مخزون افتتاحية',
                is_posted=True
            )
            db.session.add(transaction)
            db.session.commit()

        flash('تم إنشاء الصنف بنجاح', 'success')
        return redirect(url_for('inventory.items'))

    # الحصول على جميع الفئات والوحدات لعرضها في النموذج
    categories = InventoryCategory.query.all()
    units = InventoryUnit.query.all()
    return render_template('inventory/new_item.html', categories=categories, units=units)

@inventory_bp.route('/items/<item_id>')
@login_required
def item_detail(item_id):
    # الحصول على تفاصيل الصنف
    item = InventoryItem.query.get_or_404(item_id)

    # الحصول على حركات المخزون الخاصة بالصنف
    transactions = InventoryTransaction.query.filter_by(item_id=item_id).order_by(InventoryTransaction.created_at.desc()).all()

    return render_template('inventory/item_detail.html', item=item, transactions=transactions)

@inventory_bp.route('/items/<item_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_item(item_id):
    # الحصول على الصنف
    item = InventoryItem.query.get_or_404(item_id)

    if request.method == 'POST':
        # تحديث بيانات الصنف
        item.code = request.form.get('code')
        item.name = request.form.get('name')
        item.description = request.form.get('description')
        item.category_id = request.form.get('category_id')
        item.unit_id = request.form.get('unit_id')
        item.reorder_level = Decimal(request.form.get('reorder_level', 0))
        item.reorder_quantity = Decimal(request.form.get('reorder_quantity', 0))
        item.unit_cost = Decimal(request.form.get('unit_cost', 0))
        item.selling_price = Decimal(request.form.get('selling_price', 0))
        item.tax_rate = Decimal(request.form.get('tax_rate', 0))

        db.session.commit()

        flash('تم تحديث بيانات الصنف بنجاح', 'success')
        return redirect(url_for('inventory.item_detail', item_id=item_id))

    # الحصول على جميع الفئات والوحدات لعرضها في النموذج
    categories = InventoryCategory.query.all()
    units = InventoryUnit.query.all()
    return render_template('inventory/edit_item.html', item=item, categories=categories, units=units)

@inventory_bp.route('/items/<item_id>/delete', methods=['POST'])
@login_required
def delete_item(item_id):
    # الحصول على الصنف
    item = InventoryItem.query.get_or_404(item_id)

    # التحقق من عدم وجود حركات مخزون للصنف
    if item.transactions.count() > 0:
        flash('لا يمكن حذف الصنف لأن لديه حركات مخزون مسجلة', 'danger')
        return redirect(url_for('inventory.item_detail', item_id=item_id))

    db.session.delete(item)
    db.session.commit()

    flash('تم حذف الصنف بنجاح', 'success')
    return redirect(url_for('inventory.items'))

@inventory_bp.route('/categories')
@login_required
def categories():
    # الحصول على جميع فئات المخزون
    categories = InventoryCategory.query.all()
    return render_template('inventory/categories.html', categories=categories)

@inventory_bp.route('/categories/new', methods=['GET', 'POST'])
@login_required
def new_category():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        parent_id = request.form.get('parent_id') or None

        # إنشاء الفئة الجديدة
        new_category = InventoryCategory(
            name=name,
            description=description,
            parent_id=parent_id
        )

        db.session.add(new_category)
        db.session.commit()

        flash('تم إنشاء الفئة بنجاح', 'success')
        return redirect(url_for('inventory.categories'))

    # الحصول على جميع الفئات لاستخدامها كآباء محتملين
    categories = InventoryCategory.query.all()
    return render_template('inventory/new_category.html', categories=categories)

@inventory_bp.route('/transactions')
@login_required
def transactions():
    # الحصول على جميع حركات المخزون
    transactions = InventoryTransaction.query.order_by(InventoryTransaction.created_at.desc()).all()
    return render_template('inventory/transactions.html', transactions=transactions)

@inventory_bp.route('/adjustments')
@login_required
def adjustments():
    # الحصول على جميع تعديلات المخزون
    adjustments = StockAdjustment.query.order_by(StockAdjustment.created_at.desc()).all()
    return render_template('inventory/adjustments.html', adjustments=adjustments)

@inventory_bp.route('/adjustments/new', methods=['GET', 'POST'])
@login_required
def new_adjustment():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        item_id = request.form.get('item_id')
        current_quantity = Decimal(request.form.get('current_quantity', 0))
        new_quantity = Decimal(request.form.get('new_quantity', 0))
        reason = request.form.get('reason')
        notes = request.form.get('notes')

        # حساب الفارق
        difference = new_quantity - current_quantity

        # إنشاء تعديل المخزون
        adjustment = StockAdjustment(
            item_id=item_id,
            current_quantity=current_quantity,
            new_quantity=new_quantity,
            difference=difference,
            reason=reason,
            notes=notes
        )
        db.session.add(adjustment)
        db.session.flush()  # للحصول على ID التعديل

        # إنشاء حركة المخزون المقابلة
        transaction = InventoryTransaction(
            item_id=item_id,
            transaction_type='adjustment',
            quantity=difference,
            unit_cost=InventoryItem.query.get(item_id).unit_cost,
            value=difference * InventoryItem.query.get(item_id).unit_cost,
            reference_id=f'ADJ-{adjustment.id}',
            notes=f'تعديل مخزون: {reason}',
            is_posted=True
        )
        db.session.add(transaction)

        # تحديث كمية الصنف
        item = InventoryItem.query.get(item_id)
        item.current_quantity = new_quantity

        db.session.commit()

        flash('تم تسجيل تعديل المخزون بنجاح', 'success')
        return redirect(url_for('inventory.adjustments'))

    # الحصول على جميع أصناف المخزون لعرضها في النموذج
    items = InventoryItem.query.all()
    return render_template('inventory/new_adjustment.html', items=items)

@inventory_bp.route('/transfers')
@login_required
def transfers():
    # الحصول على جميع عمليات نقل المخزون
    transfers = StockTransfer.query.order_by(StockTransfer.created_at.desc()).all()
    return render_template('inventory/transfers.html', transfers=transfers)

@inventory_bp.route('/transfers/new', methods=['GET', 'POST'])
@login_required
def new_transfer():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        from_location_id = request.form.get('from_location_id')
        to_location_id = request.form.get('to_location_id')
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        notes = request.form.get('notes')

        # إنشاء عملية نقل المخزون
        transfer = StockTransfer(
            from_location_id=from_location_id,
            to_location_id=to_location_id,
            date=date,
            notes=notes
        )
        db.session.add(transfer)
        db.session.flush()  # للحصول على ID عملية النقل

        # جمع بنود عملية النقل
        total_value = Decimal('0.00')

        for key, value in request.form.items():
            if key.startswith('item_id_'):
                index = key.split('_')[2]
                item_id = value
                quantity = Decimal(request.form.get(f'item_quantity_{index}', 0))

                if quantity > 0:
                    # الحصول على تكلفة الوحدة
                    item = InventoryItem.query.get(item_id)
                    unit_cost = item.unit_cost

                    # حساب القيمة
                    value = quantity * unit_cost
                    total_value += value

                    # إنشاء بند عملية النقل
                    transfer_item = StockTransferItem(
                        stock_transfer_id=transfer.id,
                        item_id=item_id,
                        quantity=quantity,
                        unit_cost=unit_cost,
                        value=value
                    )
                    db.session.add(transfer_item)

                    # إنشاء حركتي مخزون (مخرج ومصاد)
                    # حركة الخروج
                    outbound_transaction = InventoryTransaction(
                        item_id=item_id,
                        transaction_type='transfer_out',
                        quantity=quantity,
                        unit_cost=unit_cost,
                        value=value,
                        reference_id=f'TRANSFER-{transfer.id}',
                        notes=f'نقل مخزون من مكان إلى آخر',
                        is_posted=True
                    )
                    db.session.add(outbound_transaction)

                    # حركة الدخول
                    inbound_transaction = InventoryTransaction(
                        item_id=item_id,
                        transaction_type='transfer_in',
                        quantity=quantity,
                        unit_cost=unit_cost,
                        value=value,
                        reference_id=f'TRANSFER-{transfer.id}',
                        notes=f'نقل مخزون من مكان إلى آخر',
                        is_posted=True
                    )
                    db.session.add(inbound_transaction)

        db.session.commit()

        flash('تم تسجيل عملية نقل المخزون بنجاح', 'success')
        return redirect(url_for('inventory.transfers'))

    # الحصول على جميع أصناف المخزون والأماكن لعرضها في النموذج
    items = InventoryItem.query.all()
    locations = InventoryLocation.query.all()
    return render_template('inventory/new_transfer.html', items=items, locations=locations)

@inventory_bp.route('/locations')
@login_required
def locations():
    # الحصول على جميع أماكن المخزون
    locations = InventoryLocation.query.all()
    return render_template('inventory/locations.html', locations=locations)

@inventory_bp.route('/locations/new', methods=['GET', 'POST'])
@login_required
def new_location():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        code = request.form.get('code')
        address = request.form.get('address')

        # التحقق من عدم تكرار الكود
        existing_location = InventoryLocation.query.filter_by(code=code).first()
        if existing_location:
            flash('كود المكان موجود بالفعل', 'danger')
            return render_template('inventory/new_location.html')

        # إنشاء المكان الجديد
        new_location = InventoryLocation(
            name=name,
            code=code,
            address=address
        )

        db.session.add(new_location)
        db.session.commit()

        flash('تم إنشاء المكان بنجاح', 'success')
        return redirect(url_for('inventory.locations'))

    return render_template('inventory/new_location.html')
