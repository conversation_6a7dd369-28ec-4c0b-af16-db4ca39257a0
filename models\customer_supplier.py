
from app import db
from datetime import datetime
from decimal import Decimal
import uuid

class Customer(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_id = db.Column(db.String(50))
    credit_limit = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    account = db.relationship('Account')
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic')
    payments = db.relationship('Payment', backref='customer', lazy='dynamic')

    def get_balance(self):
        # حساب رصيد العميل
        total_invoices = sum(inv.total_amount for inv in self.invoices if inv.status == 'open')
        total_payments = sum(pay.amount for pay in self.payments)
        return total_invoices - total_payments

    def __repr__(self):
        return f'<Customer {self.code}: {self.name}>'

class Supplier(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_id = db.Column(db.String(50))
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    account = db.relationship('Account')
    invoices = db.relationship('SupplierInvoice', backref='supplier', lazy='dynamic')
    payments = db.relationship('SupplierPayment', backref='supplier', lazy='dynamic')
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier', lazy='dynamic')

    def get_balance(self):
        # حساب رصيد المورد
        total_invoices = sum(inv.total_amount for inv in self.invoices if inv.status == 'open')
        total_payments = sum(pay.amount for pay in self.payments)
        return total_invoices - total_payments

    def __repr__(self):
        return f'<Supplier {self.code}: {self.name}>'

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.String(36), db.ForeignKey('customer.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    tax_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    discount_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    status = db.Column(db.String(20), default='draft')  # draft, open, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic')
    payments = db.relationship('Payment', backref='invoice', lazy='dynamic')

    def get_remaining_amount(self):
        # حساب المبلغ المتبقي للفاتورة
        total_payments = sum(pay.amount for pay in self.payments)
        return self.total_amount - total_payments

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class InvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    description = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), default=Decimal('0.00'))

    # العلاقات
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    account = db.relationship('Account')

    @property
    def total_amount(self):
        return self.quantity * self.unit_price

    @property
    def tax_amount(self):
        return self.total_amount * (self.tax_rate / 100)

    @property
    def line_total(self):
        return self.total_amount + self.tax_amount

    def __repr__(self):
        return f'<InvoiceItem {self.id}>'

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    payment_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.String(36), db.ForeignKey('customer.id'), nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=True)
    date = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank, credit
    reference_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    journal_entry = db.relationship('JournalEntry', backref='payment', uselist=False)

    def __repr__(self):
        return f'<Payment {self.payment_number}>'

class SupplierInvoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.String(36), db.ForeignKey('supplier.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    tax_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    discount_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    status = db.Column(db.String(20), default='draft')  # draft, open, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('SupplierInvoiceItem', backref='invoice', lazy='dynamic')
    payments = db.relationship('SupplierPayment', backref='invoice', lazy='dynamic')

    def get_remaining_amount(self):
        # حساب المبلغ المتبقي للفاتورة
        total_payments = sum(pay.amount for pay in self.payments)
        return self.total_amount - total_payments

    def __repr__(self):
        return f'<SupplierInvoice {self.invoice_number}>'

class SupplierInvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('supplier_invoice.id'), nullable=False)
    description = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), default=Decimal('0.00'))

    # العلاقات
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    account = db.relationship('Account')

    @property
    def total_amount(self):
        return self.quantity * self.unit_price

    @property
    def tax_amount(self):
        return self.total_amount * (self.tax_rate / 100)

    @property
    def line_total(self):
        return self.total_amount + self.tax_amount

    def __repr__(self):
        return f'<SupplierInvoiceItem {self.id}>'

class SupplierPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    payment_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.String(36), db.ForeignKey('supplier.id'), nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('supplier_invoice.id'), nullable=True)
    date = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank, credit
    reference_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    journal_entry = db.relationship('JournalEntry', backref='supplier_payment', uselist=False)

    def __repr__(self):
        return f'<SupplierPayment {self.payment_number}>'

class PurchaseOrder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.String(36), db.ForeignKey('supplier.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    expected_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, sent, received, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('PurchaseOrderItem', backref='purchase_order', lazy='dynamic')

    def __repr__(self):
        return f'<PurchaseOrder {self.order_number}>'

class PurchaseOrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_order.id'), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('inventory_item.id'), nullable=True)
    description = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)

    # العلاقات
    inventory_item = db.relationship('InventoryItem')

    @property
    def total_amount(self):
        return self.quantity * self.unit_price

    def __repr__(self):
        return f'<PurchaseOrderItem {self.id}>'
