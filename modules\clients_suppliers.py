# clients_suppliers.py
import sqlite3
from datetime import datetime

def issue_invoice(client_name, amount):
    conn = sqlite3.connect("accounting.db")
    cursor = conn.cursor()
    cursor.execute("INSERT INTO invoices (client, amount, date) VALUES (?, ?, ?)",
                   (client_name, amount, datetime.now().strftime("%Y-%m-%d")))
    conn.commit()
    conn.close()    
    
def record_payment(client_name, amount):
    conn = sqlite3.connect("accounting.db")
    cursor = conn.cursor()
    cursor.execute("INSERT INTO payments (client, amount, date) VALUES (?, ?, ?)",
                   (client_name, amount, datetime.now().strftime("%Y-%m-%d")))
    conn.commit()
    conn.close()    
def add_supplier(supplier_name, contact_info):
    conn = sqlite3.connect("accounting.db")
    cursor = conn.cursor()
    cursor.execute("INSERT INTO suppliers (name, contact_info) VALUES (?, ?)",
                   (supplier_name, contact_info))
    conn.commit()
    conn.close()    
def record_purchase(supplier_name, amount):
    conn = sqlite3.connect("accounting.db")
    cursor = conn.cursor()
    cursor.execute("INSERT INTO purchases (supplier, amount, date) VALUES (?, ?, ?)",
                   (supplier_name, amount, datetime.now().strftime("%Y-%m-%d")))
    conn.commit()
    conn.close()    
from enum import Enum

from enum import Enum

class AssetCategory(Enum):
    # الأصول الأساسية
    VEHICLE = "مركبة"
    EQUIPMENT = "معدات"
    PROPERTY = "عقار"
    FURNITURE = "أثاث"
    BUILDING = "مبنى"
    LAND = "أرض"
    INFRASTRUCTURE = "بنية تحتية"
    OTHER = "أخرى"
    
    # أنواع الأصول المتخصصة
    INTANGIBLE = "غير ملموس"
    TECHNOLOGY = "تكنولوجيا"
    DIGITAL_ASSET = "أصل رقمي"
    FINANCIAL_ASSET = "أصل مالي"
    NATURAL_RESOURCE = "موارد طبيعية"
    INTELLECTUAL_PROPERTY = "ملكية فكرية"
    
    # فئات التكنولوجيا
    SOFTWARE = "برمجيات"
    HARDWARE = "أجهزة"
    
    # فئات المكتب والمعدات
    OFFICE_SUPPLIES = "لوازم مكتبية"
    MACHINERY = "آلات"
    TOOLS = "أدوات"
    INVENTORY = "مخزون"
    
    # فئات خاصة
    ARTWORK = "عمل فني"
    COLLECTIBLES = "مقتنيات"
    LIVESTOCK = "ماشية"
    AGRICULTURAL = "زراعي"
    LANDSCAPE = "مناظرة طبيعية"
    HUMAN_RESOURCE = "موارد بشرية"
    
    # الأساطيل
    VEHICLE_FLEET = "أسطول مركبات"
    EQUIPMENT_FLEET = "أسطول معدات"
    PROPERTY_FLEET = "أسطول عقارات"
    OTHER_FLEET = "أسطول أخرى"
    
    # المجموعات المزدوجة
    VEHICLE_EQUIPMENT = "معدات مركبة"
    VEHICLE_PROPERTY = "عقار مركبة"
    EQUIPMENT_PROPERTY = "عقار معدات"
    VEHICLE_OTHER = "أخرى مركبة"
    EQUIPMENT_OTHER = "أخرى معدات"
    PROPERTY_OTHER = "أخرى عقار"
    INFRASTRUCTURE_VEHICLE = "بنية تحتية مركبة"
    INFRASTRUCTURE_EQUIPMENT = "بنية تحتية معدات"
    INFRASTRUCTURE_PROPERTY = "بنية تحتية عقار"
    INFRASTRUCTURE_OTHER = "بنية تحتية أخرى"
    
    # المجموعات الثلاثية
    VEHICLE_EQUIPMENT_PROPERTY = "مركبة معدات عقار"
    VEHICLE_EQUIPMENT_OTHER = "مركبة معدات أخرى"
    VEHICLE_PROPERTY_OTHER = "مركبة عقار أخرى"
    EQUIPMENT_PROPERTY_OTHER = "معدات عقار أخرى"
    INFRASTRUCTURE_VEHICLE_EQUIPMENT = "بنية تحتية مركبة معدات"
    INFRASTRUCTURE_VEHICLE_PROPERTY = "بنية تحتية مركبة عقار"
    INFRASTRUCTURE_VEHICLE_OTHER = "بنية تحتية مركبة أخرى"
    INFRASTRUCTURE_EQUIPMENT_PROPERTY = "بنية تحتية معدات عقار"
    INFRASTRUCTURE_EQUIPMENT_OTHER = "بنية تحتية معدات أخرى"
    INFRASTRUCTURE_PROPERTY_OTHER = "بنية تحتية عقار أخرى"
    
    # المجموعات الرباعية
    VEHICLE_EQUIPMENT_PROPERTY_OTHER = "مركبة معدات عقار أخرى"
    INFRASTRUCTURE_VEHICLE_EQUIPMENT_PROPERTY = "بنية تحتية مركبة معدات عقار"
    INFRASTRUCTURE_VEHICLE_EQUIPMENT_OTHER = "بنية تحتية مركبة معدات أخرى"
    INFRASTRUCTURE_VEHICLE_PROPERTY_OTHER = "بنية تحتية مركبة عقار أخرى"
    INFRASTRUCTURE_EQUIPMENT_PROPERTY_OTHER = "بنية تحتية معدات عقار أخرى"
    INFRASTRUCTURE_VEHICLE_EQUIPMENT_PROPERTY_OTHER = "بنية تحتية مركبة معدات عقار أخرى"
    
    # فئات أخرى مدمجة
    OTHER_INFRASTRUCTURE = "أخرى بنية تحتية"
    OTHER_VEHICLE = "أخرى مركبة"
    OTHER_EQUIPMENT = "أخرى معدات"
    OTHER_PROPERTY = "أخرى عقار"
    OTHER_OTHER = "أخرى أخرى"
    
    # الأصول المختلطة
    MIXED_ASSET = "أصل مختلط"
    INFRASTRUCTURE_MIXED = "مختلط بنية تحتية"
    VEHICLE_MIXED = "مختلط مركبة"
    EQUIPMENT_MIXED = "مختلط معدات"
    PROPERTY_MIXED = "مختلط عقار"
    OTHER_MIXED = "مختلط أخرى"

class AssetStatus(Enum):
    # الحالات الأساسية
    ACTIVE = "نشط"
    INACTIVE = "غير نشط"
    OTHER = "أخرى"
    
    # حالات الصيانة
    UNDER_MAINTENANCE = "تحت الصيانة"
    SCHEDULED_FOR_MAINTENANCE = "مجدول للصيانة"
    IN_REPAIR = "قيد الصيانة"
    SCHEDULED_FOR_REPAIR = "مجدول للصيانة"
    
    # حالات التصرف
    RETIRED = "متقاعد"
    DISPOSED = "منتهية"
    SOLD = "مباع"
    LEASED = "مؤجر"
    TRANSFERRED = "منقول"
    WRITTEN_OFF = "مشطوب"
    SCHEDULED_FOR_DISPOSAL = "مجدول للتصرف"
    SCHEDULED_FOR_TRANSFER = "مجدول للنقل"
    IN_DISPOSAL = "قيد التصرف"
    IN_TRANSFER = "قيد النقل"
    
    # حالات الاستبدال
    SCHEDULED_FOR_REPLACEMENT = "مجدول للاستبدال"
    IN_REPLACEMENT = "قيد الاستبدال"
    
    # حالات خاصة
    LOST = "مفقود"
    STOLEN = "مسروق"
    DAMAGED = "تالف"
    DECOMMISSIONED = "خارج الخدمة"
    IN_STORAGE = "في المخزن"
    ON_LOAN = "في القرض"
    IN_SERVICE = "في الخدمة"
    ARCHIVED = "مؤرشف"
    
    # حالات الموافقة
    PENDING = "قيد الانتظار"
    APPROVED = "موافق عليه"
    REJECTED = "مرفوض"
    
    # حالات أخرى
    OTHER_IN_PROCESS = "أخرى قيد المعالجة"
    OTHER_STATUS = "أخرى"
