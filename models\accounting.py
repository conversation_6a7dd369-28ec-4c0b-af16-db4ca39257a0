from datetime import datetime
from decimal import Decimal
from enum import Enum

# تأجيل استيراد db حتى يتم استيراد النماذج في app.py
db = None

class AccountType(Enum):
    ASSET = 'asset'
    LIABILITY = 'liability'
    EQUITY = 'equity'
    REVENUE = 'revenue'
    EXPENSE = 'expense'

class Account:
    pass

class JournalEntry:
    pass

class Transaction:
    pass

class BankReconciliation:
    pass

class ReconciliationItem:
    pass

def init_accounting_models(database):
    """تهيئة نماذج المحاسبة مع كائن قاعدة البيانات
    """
    global db
    db = database

    # إضافة حقول Account
    Account.id = db.Column(db.Integer, primary_key=True)
    Account.account_number = db.Column(db.String(20), unique=True, nullable=False)
    Account.name = db.Column(db.String(100), nullable=False)
    Account.account_type = db.Column(db.Enum(AccountType), nullable=False)
    Account.parent_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    Account.is_active = db.Column(db.Boolean, default=True)
    Account.created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    Account.children = db.relationship('Account', remote_side=[Account.id])
    Account.parent = db.relationship('Account', remote_side=[Account.id], backref=db.backref('children', lazy='dynamic'))
    Account.journal_entries = db.relationship('JournalEntry', backref='account', lazy='dynamic')

    # إضافة حقول JournalEntry
    JournalEntry.id = db.Column(db.Integer, primary_key=True)
    JournalEntry.reference_number = db.Column(db.String(50), unique=True, nullable=False)
    JournalEntry.date = db.Column(db.Date, nullable=False)
    JournalEntry.description = db.Column(db.Text)
    JournalEntry.account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False)
    JournalEntry.is_debit = db.Column(db.Boolean, nullable=False)
    JournalEntry.amount = db.Column(db.Numeric(15, 2), nullable=False)
    JournalEntry.created_at = db.Column(db.DateTime, default=datetime.utcnow)
    JournalEntry.created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    JournalEntry.transaction = db.relationship('Transaction', backref='journal_entries', lazy='dynamic')

    # إضافة حقول Transaction
    Transaction.id = db.Column(db.Integer, primary_key=True)
    Transaction.reference_number = db.Column(db.String(50), unique=True, nullable=False)
    Transaction.date = db.Column(db.Date, nullable=False)
    Transaction.description = db.Column(db.Text)
    Transaction.amount = db.Column(db.Numeric(15, 2), nullable=False)
    Transaction.status = db.Column(db.String(20), default='pending')  # pending, posted, reconciled
    Transaction.created_at = db.Column(db.DateTime, default=datetime.utcnow)
    Transaction.created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    Transaction.journal_entries = db.relationship('JournalEntry', backref='transaction', lazy='dynamic')

    # إضافة حقول BankReconciliation
    BankReconciliation.id = db.Column(db.Integer, primary_key=True)
    BankReconciliation.account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False)
    BankReconciliation.bank_statement_date = db.Column(db.Date, nullable=False)
    BankReconciliation.bank_statement_balance = db.Column(db.Numeric(15, 2), nullable=False)
    BankReconciliation.book_balance = db.Column(db.Numeric(15, 2), nullable=False)
    BankReconciliation.difference = db.Column(db.Numeric(15, 2), nullable=False)
    BankReconciliation.notes = db.Column(db.Text)
    BankReconciliation.created_at = db.Column(db.DateTime, default=datetime.utcnow)
    BankReconciliation.created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    BankReconciliation.account = db.relationship('Account')
    BankReconciliation.reconciled_items = db.relationship('ReconciliationItem', backref='reconciliation', lazy='dynamic')

    # إضافة حقول ReconciliationItem
    ReconciliationItem.id = db.Column(db.Integer, primary_key=True)
    ReconciliationItem.reconciliation_id = db.Column(db.Integer, db.ForeignKey('bank_reconciliation.id'), nullable=False)
    ReconciliationItem.journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entry.id'), nullable=False)
    ReconciliationItem.amount = db.Column(db.Numeric(15, 2), nullable=False)
    ReconciliationItem.is_matched = db.Column(db.Boolean, default=False)
    ReconciliationItem.notes = db.Column(db.Text)

    # العلاقات
    ReconciliationItem.journal_entry = db.relationship('JournalEntry')

def get_balance(self, end_date=None):
    # حساب رصيد الحساب حتى تاريخ معين
    debit_total = Decimal(0)
    credit_total = Decimal(0)

    if end_date:
        debit_entries = self.journal_entries.filter(
            db.and_(JournalEntry.is_debit == True, JournalEntry.date <= end_date)
        ).all()
        credit_entries = self.journal_entries.filter(
            db.and_(JournalEntry.is_debit == False, JournalEntry.date <= end_date)
        ).all()
    else:
        debit_entries = self.journal_entries.filter_by(is_debit=True).all()
        credit_entries = self.journal_entries.filter_by(is_debit=False).all()

    for entry in debit_entries:
        debit_total += entry.amount
    for entry in credit_entries:
        credit_total += entry.amount

    # إذا كان الحساب من نوع أصول أو مصروفات، فالرصيد يكون مديناً
    if self.account_type in [AccountType.ASSET, AccountType.EXPENSE]:
        return debit_total - credit_total
    # إذا كان الحساب من نوع التزامات أو حقوق أو إيرادات، فالرصيد يكون دائناً
    else:
        return credit_total - debit_total

def __repr__(self):
    return f'<Account {self.account_number}: {self.name}>'

def journal_entry_repr(self):
    return f'<JournalEntry {self.reference_number}>'

def transaction_repr(self):
    return f'<Transaction {self.reference_number}>'

def bank_reconciliation_repr(self):
    return f'<BankReconciliation {self.bank_statement_date}>'

def reconciliation_item_repr(self):
    return f'<ReconciliationItem {self.id}>'

def transaction_is_balanced(self):
    # التحقق من أن المجموع المدي يساوي المجموم الدائن
    debit_total = Decimal(0)
    credit_total = Decimal(0)

    for entry in self.journal_entries:
        if entry.is_debit:
            debit_total += entry.amount
        else:
            credit_total += entry.amount

    return debit_total == credit_total

# إضافة الدوال إلى الفئات
Account.get_balance = get_balance
Account.__repr__ = __repr__
JournalEntry.__repr__ = journal_entry_repr
Transaction.__repr__ = transaction_repr
Transaction.is_balanced = transaction_is_balanced
BankReconciliation.__repr__ = bank_reconciliation_repr
ReconciliationItem.__repr__ = reconciliation_item_repr
