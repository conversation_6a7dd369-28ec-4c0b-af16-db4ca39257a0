
from app import db
from datetime import datetime, date
from decimal import Decimal
import uuid

class AssetCategory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    depreciation_method = db.Column(db.String(20), default='straight_line')  # straight_line, reducing_balance, units_of_production
    useful_life = db.Column(db.Integer, nullable=False)  # in years
    residual_value_percent = db.Column(db.Numeric(5, 2), default=Decimal('0.00'))
    is_active = db.Column(db.<PERSON><PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    assets = db.relationship('Asset', backref='category', lazy='dynamic')

    def __repr__(self):
        return f'<AssetCategory {self.name}>'

class Asset(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    asset_number = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('asset_category.id'), nullable=False)
    purchase_date = db.Column(db.Date, nullable=False)
    purchase_price = db.Column(db.Numeric(15, 2), nullable=False)
    useful_life = db.Column(db.Integer, nullable=False)  # in years
    residual_value = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    depreciation_method = db.Column(db.String(20), default='straight_line')  # straight_line, reducing_balance, units_of_production
    current_value = db.Column(db.Numeric(15, 2), nullable=False)
    accumulated_depreciation = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    status = db.Column(db.String(20), default='active')  # active, disposed, under_repair
    location = db.Column(db.String(200))
    responsible_person = db.Column(db.String(100))
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendor.id'), nullable=True)
    warranty_expiry = db.Column(db.Date, nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    category = db.relationship('AssetCategory')
    vendor = db.relationship('Vendor')
    depreciations = db.relationship('Depreciation', backref='asset', lazy='dynamic')

    def get_book_value(self):
        return self.current_value - self.accumulated_depreciation

    def __repr__(self):
        return f'<Asset {self.asset_number}: {self.name}>'

class Vendor(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_id = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    assets = db.relationship('Asset', backref='vendor', lazy='dynamic')

    def __repr__(self):
        return f'<Vendor {self.name}>'

class Depreciation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    asset_id = db.Column(db.String(36), db.ForeignKey('asset.id'), nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)  # 1-12 for monthly
    depreciation_amount = db.Column(db.Numeric(15, 2), nullable=False)
    accumulated_depreciation = db.Column(db.Numeric(15, 2), nullable=False)
    book_value = db.Column(db.Numeric(15, 2), nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    asset = db.relationship('Asset')

    def __repr__(self):
        return f'<Depreciation {self.id}>'

class AssetDisposal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    asset_id = db.Column(db.String(36), db.ForeignKey('asset.id'), nullable=False)
    disposal_date = db.Column(db.Date, nullable=False)
    disposal_method = db.Column(db.String(50), nullable=False)  # sold, scrapped, donated
    disposal_value = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    buyer_name = db.Column(db.String(100))
    reason = db.Column(db.Text, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    asset = db.relationship('Asset')

    def __repr__(self):
        return f'<AssetDisposal {self.id}>'

class AssetTransfer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    asset_id = db.Column(db.String(36), db.ForeignKey('asset.id'), nullable=False)
    from_location = db.Column(db.String(100), nullable=False)
    to_location = db.Column(db.String(100), nullable=False)
    transfer_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    asset = db.relationship('Asset')

    def __repr__(self):
        return f'<AssetTransfer {self.id}>'
