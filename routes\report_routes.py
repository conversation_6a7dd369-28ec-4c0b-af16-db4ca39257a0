
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from app import db
from models import Account, JournalEntry, Transaction, FinancialReport, ReportSection, BalanceSheet, IncomeStatement, CashFlowStatement, Budget, BudgetItem, TaxReport
from decimal import Decimal
from datetime import datetime, date
import pandas as pd
import io
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

report_bp = Blueprint('reports', __name__)

@report_bp.route('/')
@login_required
def reports_dashboard():
    # الحصول على إحصائيات لوحة التحكم التقارير
    stats = {
        'financial_reports': FinancialReport.query.count(),
        'balance_sheets': BalanceSheet.query.count(),
        'income_statements': IncomeStatement.query.count(),
        'cash_flow_statements': CashFlowStatement.query.count(),
        'budgets': Budget.query.count(),
        'tax_reports': TaxReport.query.count()
    }

    return render_template('reports/dashboard.html', stats=stats)

@report_bp.route('/financial-reports')
@login_required
def financial_reports():
    # الحصول على جميع التقارير المالية
    reports = FinancialReport.query.order_by(FinancialReport.created_at.desc()).all()
    return render_template('reports/financial_reports.html', reports=reports)

@report_bp.route('/financial-reports/new', methods=['GET', 'POST'])
@login_required
def new_financial_report():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        report_type = request.form.get('report_type')
        report_name = request.form.get('report_name')
        report_date = datetime.strptime(request.form.get('report_date'), '%Y-%m-%d').date()
        fiscal_year = int(request.form.get('fiscal_year'))
        fiscal_period = int(request.form.get('fiscal_period'))
        fiscal_quarter = int(request.form.get('fiscal_quarter')) if request.form.get('fiscal_quarter') else None
        notes = request.form.get('notes')

        # إنشاء التقرير المالي الجديد
        new_report = FinancialReport(
            report_type=report_type,
            report_name=report_name,
            report_date=report_date,
            fiscal_year=fiscal_year,
            fiscal_period=fiscal_period,
            fiscal_quarter=fiscal_quarter,
            generated_by_id=current_user.id,
            is_draft=False,
            notes=notes
        )

        db.session.add(new_report)
        db.session.flush()  # للحصول على ID التقرير

        # إنشاء أقسام التقرير بناءً على نوعه
        if report_type == 'balance_sheet':
            # إنشاء تقرير الميزانية العمومية
            balance_sheet = generate_balance_sheet(fiscal_year, fiscal_period)

            # إضافة أقسام التقرير
            sections = [
                {'name': 'الأصول', 'order': 1, 'is_summary': False, 'content': balance_sheet['assets']},
                {'name': 'الالتزامات', 'order': 2, 'is_summary': False, 'content': balance_sheet['liabilities']},
                {'name': 'حقوق الملكية', 'order': 3, 'is_summary': False, 'content': balance_sheet['equity']}
            ]

        elif report_type == 'income_statement':
            # إنشاء تقرير قائمة الدخل
            income_statement = generate_income_statement(fiscal_year, fiscal_period)

            # إضافة أقسام التقرير
            sections = [
                {'name': 'الإيرادات', 'order': 1, 'is_summary': False, 'content': income_statement['revenues']},
                {'name': 'التكاليف والمصروفات', 'order': 2, 'is_summary': False, 'content': income_statement['expenses']},
                {'name': 'صافي الربح', 'order': 3, 'is_summary': True, 'content': income_statement['net_income']}
            ]

        elif report_type == 'cash_flow_statement':
            # إنشاء تقرير قائمة التدفقات النقدية
            cash_flow_statement = generate_cash_flow_statement(fiscal_year, fiscal_period)

            # إضافة أقسام التقرير
            sections = [
                {'name': 'التدفقات النقدية من العمليات', 'order': 1, 'is_summary': False, 'content': cash_flow_statement['operating']},
                {'name': 'التدفقات النقدية من الاستثمار', 'order': 2, 'is_summary': False, 'content': cash_flow_statement['investing']},
                {'name': 'التدفقات النقدية من التمويل', 'order': 3, 'is_summary': False, 'content': cash_flow_statement['financing']},
                {'name': 'التغير في النقد', 'order': 4, 'is_summary': True, 'content': cash_flow_statement['net_change']}
            ]

        # حفظ أقسام التقرير
        for section in sections:
            report_section = ReportSection(
                financial_report_id=new_report.id,
                section_name=section['name'],
                section_order=section['order'],
                is_summary=section['is_summary'],
                content=str(section['content'])
            )
            db.session.add(report_section)

        db.session.commit()

        flash('تم إنشاء التقرير المالي بنجاح', 'success')
        return redirect(url_for('reports.financial_reports'))

    return render_template('reports/new_financial_report.html')

@report_bp.route('/balance-sheet')
@login_required
def balance_sheet():
    # الحصول على التاريخ المحدد للتقرير
    report_date_str = request.args.get('date', date.today().strftime('%Y-%m-%d'))
    report_date = datetime.strptime(report_date_str, '%Y-%m-%d').date()

    # إنشاء تقرير الميزانية العمومية
    balance_sheet = generate_balance_sheet(report_date.year, report_date.month)

    return render_template('reports/balance_sheet.html', balance_sheet=balance_sheet, report_date=report_date)

@report_bp.route('/income-statement')
@login_required
def income_statement():
    # الحصول على الفترة المحددة للتقرير
    year = int(request.args.get('year', date.today().year))
    period = int(request.args.get('period', date.today().month))

    # إنشاء تقرير قائمة الدخل
    income_statement = generate_income_statement(year, period)

    return render_template('reports/income_statement.html', income_statement=income_statement, year=year, period=period)

@report_bp.route('/cash-flow-statement')
@login_required
def cash_flow_statement():
    # الحصول على الفترة المحددة للتقرير
    year = int(request.args.get('year', date.today().year))
    period = int(request.args.get('period', date.today().month))

    # إنشاء تقرير قائمة التدفقات النقدية
    cash_flow_statement = generate_cash_flow_statement(year, period)

    return render_template('reports/cash_flow_statement.html', cash_flow_statement=cash_flow_statement, year=year, period=period)

@report_bp.route('/trial-balance')
@login_required
def trial_balance():
    # الحصول على التاريخ المحدد للتقرير
    report_date_str = request.args.get('date', date.today().strftime('%Y-%m-%d'))
    report_date = datetime.strptime(report_date_str, '%Y-%m-%d').date()

    # إنشاء قائمة المحاسبين
    trial_balance = generate_trial_balance(report_date)

    return render_template('reports/trial_balance.html', trial_balance=trial_balance, report_date=report_date)

@report_bp.route('/budgets')
@login_required
def budgets():
    # الحصول على جميع الميزانيات
    budgets = Budget.query.order_by(Budget.created_at.desc()).all()
    return render_template('reports/budgets.html', budgets=budgets)

@report_bp.route('/budgets/new', methods=['GET', 'POST'])
@login_required
def new_budget():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        fiscal_year = int(request.form.get('fiscal_year'))
        description = request.form.get('description')

        # إنشاء الميزانية الجديدة
        new_budget = Budget(
            name=name,
            fiscal_year=fiscal_year,
            description=description,
            status='draft',
            created_by_id=current_user.id
        )

        db.session.add(new_budget)
        db.session.flush()  # للحصول على ID الميزانية

        # إضافة بنود الميزانية
        for key, value in request.form.items():
            if key.startswith('account_'):
                account_id = int(key.split('_')[1])
                amount = Decimal(value)

                if amount > 0:
                    budget_item = BudgetItem(
                        budget_id=new_budget.id,
                        account_id=account_id,
                        period=1,  # افتراضي للشهر الأول
                        amount=amount
                    )
                    db.session.add(budget_item)

        db.session.commit()

        flash('تم إنشاء الميزانية بنجاح', 'success')
        return redirect(url_for('reports.budgets'))

    # الحصول على جميع الحسابات لعرضها في النموذج
    accounts = Account.query.all()
    return render_template('reports/new_budget.html', accounts=accounts)

@report_bp.route('/budget-analysis')
@login_required
def budget_analysis():
    # الحصول على الميزانية والفترة المحددة
    budget_id = request.args.get('budget_id')
    period = int(request.args.get('period', 1))

    if not budget_id:
        flash('الرجاء تحديد ميزانية', 'danger')
        return redirect(url_for('reports.budgets'))

    # الحصول على تفاصيل الميزانية
    budget = Budget.query.get(budget_id)
    if not budget:
        flash('الميزانية المحددة غير موجودة', 'danger')
        return redirect(url_for('reports.budgets'))

    # الحصول على بنود الميزانية للفترة المحددة
    budget_items = BudgetItem.query.filter_by(budget_id=budget_id, period=period).all()

    # حساب الفروقات بين الميزانية والفعلي
    analysis = []
    for item in budget_items:
        # حساب المبلغ الفعلي للحساب حتى تاريخ معين
        actual_amount = get_account_actual_amount(item.account_id, budget.fiscal_year, period)

        # حساب الفارق والنسبة المئوية
        variance = actual_amount - item.amount
        variance_percent = (variance / item.amount * 100) if item.amount > 0 else 0

        # إضافة البيانات إلى التحليل
        analysis.append({
            'account': item.account,
            'budget_amount': item.amount,
            'actual_amount': actual_amount,
            'variance': variance,
            'variance_percent': variance_percent
        })

    return render_template('reports/budget_analysis.html', budget=budget, period=period, analysis=analysis)

@report_bp.route('/tax-reports')
@login_required
def tax_reports():
    # الحصول على جميع التقارير الضريبية
    tax_reports = TaxReport.query.order_by(TaxReport.created_at.desc()).all()
    return render_template('reports/tax_reports.html', tax_reports=tax_reports)

@report_bp.route('/tax-reports/new', methods=['GET', 'POST'])
@login_required
def new_tax_report():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        tax_type = request.form.get('tax_type')
        report_period = request.form.get('report_period')
        fiscal_year = int(request.form.get('fiscal_year'))
        fiscal_period = int(request.form.get('fiscal_period'))
        taxable_amount = Decimal(request.form.get('taxable_amount', 0))
        tax_rate = Decimal(request.form.get('tax_rate', 0))
        tax_amount = taxable_amount * (tax_rate / 100)
        filing_date = datetime.strptime(request.form.get('filing_date'), '%Y-%m-%d').date() if request.form.get('filing_date') else None
        payment_date = datetime.strptime(request.form.get('payment_date'), '%Y-%m-%d').date() if request.form.get('payment_date') else None
        notes = request.form.get('notes')

        # إنشاء التقرير الضريبي الجديد
        new_tax_report = TaxReport(
            tax_type=tax_type,
            report_period=report_period,
            fiscal_year=fiscal_year,
            fiscal_period=fiscal_period,
            tax_amount=tax_amount,
            taxable_amount=taxable_amount,
            tax_rate=tax_rate,
            filing_date=filing_date,
            payment_date=payment_date,
            status='pending',
            notes=notes,
            created_by_id=current_user.id
        )

        db.session.add(new_tax_report)
        db.session.commit()

        flash('تم إنشاء التقرير الضريبي بنجاح', 'success')
        return redirect(url_for('reports.tax_reports'))

    return render_template('reports/new_tax_report.html')

# وظائف مساعدة لتوليد التقارير
def generate_balance_sheet(year, period):
    # توليد بيانات الميزانية العمومية
    # سيتم تنفيذ الاستعلامات اللازمة لجمع البيانات من قاعدة البيانات

    # حساب إجمالي الأصول
    total_current_assets = Decimal('0.00')
    total_non_current_assets = Decimal('0.00')
    total_assets = Decimal('0.00')

    # حساب إجمالي الالتزامات
    total_current_liabilities = Decimal('0.00')
    total_non_current_liabilities = Decimal('0.00')
    total_liabilities = Decimal('0.00')

    # حساب إجمالي حقوق الملكية
    total_equity = Decimal('0.00')

    # في تطبيق حقيقي، سيتم استعلام قاعدة البيانات للحصول على هذه القيم
    # هنا سنقوم بإنشاء بيانات وهمية للتوضيح

    # الأصول المتداولة
    cash_and_equivalents = Decimal('150000.00')
    accounts_receivable = Decimal('200000.00')
    inventory = Decimal('300000.00')
    prepaid_expenses = Decimal('50000.00')

    # إجمالي الأصول المتداولة
    total_current_assets = cash_and_equivalents + accounts_receivable + inventory + prepaid_expenses

    # الأصول غير المتداولة
    property_plant_equipment = Decimal('800000.00')
    intangible_assets = Decimal('100000.00')
    investments = Decimal('50000.00')

    # إجمالي الأصول غير المتداولة
    total_non_current_assets = property_plant_equipment + intangible_assets + investments

    # إجمالي الأصول
    total_assets = total_current_assets + total_non_current_assets

    # الالتزامات المتداولة
    accounts_payable = Decimal('100000.00')
    short_term_loans = Decimal('150000.00')
    current_portion_of_long_term_debt = Decimal('50000.00')

    # إجمالي الالتزامات المتداولة
    total_current_liabilities = accounts_payable + short_term_loans + current_portion_of_long_term_debt

    # الالتزامات غير المتداولة
    long_term_loans = Decimal('300000.00')
    deferred_tax_liabilities = Decimal('20000.00')

    # إجمالي الالتزامات غير المتداولة
    total_non_current_liabilities = long_term_loans + deferred_tax_liabilities

    # إجمالي الالتزامات
    total_liabilities = total_current_liabilities + total_non_current_liabilities

    # حقوق الملكية
    share_capital = Decimal('500000.00')
    retained_earnings = Decimal('280000.00')

    # إجمالي حقوق الملكية
    total_equity = share_capital + retained_earnings

    # التحقق من توازن الميزانية العمومية
    balance_check = total_assets - (total_liabilities + total_equity)

    return {
        'assets': {
            'current': {
                'cash_and_equivalents': cash_and_equivalents,
                'accounts_receivable': accounts_receivable,
                'inventory': inventory,
                'prepaid_expenses': prepaid_expenses,
                'total': total_current_assets
            },
            'non_current': {
                'property_plant_equipment': property_plant_equipment,
                'intangible_assets': intangible_assets,
                'investments': investments,
                'total': total_non_current_assets
            },
            'total': total_assets
        },
        'liabilities': {
            'current': {
                'accounts_payable': accounts_payable,
                'short_term_loans': short_term_loans,
                'current_portion_of_long_term_debt': current_portion_of_long_term_debt,
                'total': total_current_liabilities
            },
            'non_current': {
                'long_term_loans': long_term_loans,
                'deferred_tax_liabilities': deferred_tax_liabilities,
                'total': total_non_current_liabilities
            },
            'total': total_liabilities
        },
        'equity': {
            'share_capital': share_capital,
            'retained_earnings': retained_earnings,
            'total': total_equity
        },
        'balance_check': balance_check
    }

def generate_income_statement(year, period):
    # توليد بيانات قائمة الدخل
    # سيتم تنفيذ الاستعلامات اللازمة لجمع البيانات من قاعدة البيانات

    # في تطبيق حقيقي، سيتم استعلام قاعدة البيانات للحصول على هذه القيم
    # هنا سنقوم بإنشاء بيانات وهمية للتوضيح

    # الإيرادات
    sales_revenue = Decimal('1000000.00')
    service_revenue = Decimal('200000.00')
    other_revenue = Decimal('50000.00')

    # إجمالي الإيرادات
    total_revenue = sales_revenue + service_revenue + other_revenue

    # التكاليف
    cost_of_sales = Decimal('400000.00')

    # الربح الإجمالي
    gross_profit = total_revenue - cost_of_sales

    # المصروفات التشغيلية
    selling_expenses = Decimal('150000.00')
    administrative_expenses = Decimal('100000.00')
    depreciation_expense = Decimal('50000.00')
    amortization_expense = Decimal('20000.00')

    # إجمالي المصروفات التشغيلية
    total_operating_expenses = selling_expenses + administrative_expenses + depreciation_expense + amortization_expense

    # الدخل من العمليات
    operating_income = gross_profit - total_operating_expenses

    # الدخل غير التشغيلي
    interest_income = Decimal('10000.00')
    interest_expense = Decimal('20000.00')
    other_income = Decimal('15000.00')
    other_expenses = Decimal('10000.00')

    # إجمالي الدخل غير التشغيلي
    non_operating_income = interest_income + other_income
    non_operating_expenses = interest_expense + other_expenses

    # الربح قبل الضريبة
    profit_before_tax = operating_income + non_operating_income - non_operating_expenses

    # الضرائب
    income_tax_expense = profit_before_tax * Decimal('0.25')  # افتراضي 25%

    # صافي الربح
    net_income = profit_before_tax - income_tax_expense

    return {
        'revenues': {
            'sales_revenue': sales_revenue,
            'service_revenue': service_revenue,
            'other_revenue': other_revenue,
            'total': total_revenue
        },
        'costs': {
            'cost_of_sales': cost_of_sales,
            'gross_profit': gross_profit
        },
        'operating_expenses': {
            'selling_expenses': selling_expenses,
            'administrative_expenses': administrative_expenses,
            'depreciation_expense': depreciation_expense,
            'amortization_expense': amortization_expense,
            'total': total_operating_expenses
        },
        'operating_income': operating_income,
        'non_operating': {
            'income': {
                'interest_income': interest_income,
                'other_income': other_income,
                'total': non_operating_income
            },
            'expenses': {
                'interest_expense': interest_expense,
                'other_expenses': other_expenses,
                'total': non_operating_expenses
            }
        },
        'profit_before_tax': profit_before_tax,
        'tax': {
            'income_tax_expense': income_tax_expense
        },
        'net_income': net_income
    }

def generate_cash_flow_statement(year, period):
    # توليد بيانات قائمة التدفقات النقدية
    # سيتم تنفيذ الاستعلامات اللازمة لجمع البيانات من قاعدة البيانات

    # في تطبيق حقيقي، سيتم استعلام قاعدة البيانات للحصول على هذه القيم
    # هنا سنقوم بإنشاء بيانات وهمية للتوضيح

    # التدفقات النقدية من العمليات
    net_income = Decimal('300000.00')  # من قائمة الدخل
    depreciation_and_amortization = Decimal('70000.00')
    changes_in_working_capital = Decimal('50000.00')

    # إجمالي التدفقات النقدية من العمليات
    cash_from_operating_activities = net_income + depreciation_and_amortization + changes_in_working_capital

    # التدفقات النقدية من الاستثمار
    capital_expenditures = Decimal('200000.00')
    proceeds_from_asset_sales = Decimal('50000.00')
    acquisitions = Decimal('100000.00')

    # إجمالي التدفقات النقدية من الاستثمار
    cash_from_investing_activities = proceeds_from_asset_sales - capital_expenditures - acquisitions

    # التدفقات النقدية من التمويل
    proceeds_from_debt = Decimal('150000.00')
    debt_repayments = Decimal('50000.00')
    proceeds_from_equity = Decimal('100000.00')
    dividends_paid = Decimal('30000.00')

    # إجمالي التدفقات النقدية من التمويل
    cash_from_financing_activities = proceeds_from_debt + proceeds_from_equity - debt_repayments - dividends_paid

    # التغير في النقد
    net_cash_flow = cash_from_operating_activities + cash_from_investing_activities + cash_from_financing_activities

    # بداية ونهاية النقد
    cash_at_beginning = Decimal('100000.00')
    cash_at_end = cash_at_beginning + net_cash_flow

    return {
        'operating': {
            'net_income': net_income,
            'depreciation_and_amortization': depreciation_and_amortization,
            'changes_in_working_capital': changes_in_working_capital,
            'total': cash_from_operating_activities
        },
        'investing': {
            'capital_expenditures': capital_expenditures,
            'proceeds_from_asset_sales': proceeds_from_asset_sales,
            'acquisitions': acquisitions,
            'total': cash_from_investing_activities
        },
        'financing': {
            'proceeds_from_debt': proceeds_from_debt,
            'debt_repayments': debt_repayments,
            'proceeds_from_equity': proceeds_from_equity,
            'dividends_paid': dividends_paid,
            'total': cash_from_financing_activities
        },
        'net_change': net_cash_flow,
        'cash_at_beginning': cash_at_beginning,
        'cash_at_end': cash_at_end
    }

def generate_trial_balance(report_date):
    # توليد قائمة المحاسبين
    # سيتم تنفيذ الاستعلامات اللازمة لجمع البيانات من قاعدة البيانات

    # في تطبيق حقيقي، سيتم استعلام قاعدة البيانات للحصول على هذه القيم
    # هنا سنقوم بإنشاء بيانات وهمية للتوضيح

    # الحصول على جميع الحسابات
    accounts = Account.query.all()

    # حساب الرصيد لكل حساب
    trial_balance = []
    total_debit = Decimal('0.00')
    total_credit = Decimal('0.00')

    for account in accounts:
        balance = account.get_balance(report_date)

        if account.account_type in ['asset', 'expense']:
            debit_balance = abs(balance) if balance > 0 else Decimal('0.00')
            credit_balance = Decimal('0.00') if balance > 0 else abs(balance)
        else:
            debit_balance = Decimal('0.00') if balance > 0 else abs(balance)
            credit_balance = abs(balance) if balance > 0 else Decimal('0.00')

        trial_balance.append({
            'account_number': account.account_number,
            'account_name': account.name,
            'debit': debit_balance,
            'credit': credit_balance
        })

        total_debit += debit_balance
        total_credit += credit_balance

    return {
        'accounts': trial_balance,
        'total_debit': total_debit,
        'total_credit': total_credit,
        'difference': total_debit - total_credit
    }

def get_account_actual_amount(account_id, year, period):
    # حساب المبلغ الفعلي للحساب حتى تاريخ معين
    # سيتم تنفيذ الاستعلامات اللازمة لجمع البيانات من قاعدة البيانات

    # في تطبيق حقيقي، سيتم استعلام قاعدة البيانات للحصول على هذه القيم
    # هنا سنقوم بإنشاء بيانات وهمية للتوضيح

    # حساب تاريخ نهاية الفترة
    if period == 12:
        end_date = date(year, 12, 31)
    else:
        end_date = date(year, period + 1, 1) - timedelta(days=1)

    # في تطبيق حقيقي، سيتم حساب المبلغ الفعلي من قاعدة البيانات
    # هنا سنقوم بإرجاع مبلغ وهمي
    return Decimal('50000.00')

# وظيفة لتوليد وتنزيل PDF للتقرير
@report_bp.route('/download-pdf/<report_type>')
@login_required
def download_pdf_report(report_type):
    # إنشاء مستند PDF
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))

    # الحصول على عناصر المستند
    elements = []

    # إضافة العنوان
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=1  # center
    )

    if report_type == 'balance_sheet':
        title = Paragraph("الميزانية العمومية", title_style)
        elements.append(title)

        # توليد بيانات الميزانية العمومية
        balance_sheet = generate_balance_sheet(date.today().year, date.today().month)

        # إنشاء جدول الميزانية العمومية
        data = [
            ['الأصول', '', 'الالتزامات والحقوق', ''],
            ['', '', '', ''],
            ['الأصول المتداولة:', '', 'الالتزامات المتداولة:', ''],
            ['النقد والمعادلات النقدية:', format_currency(balance_sheet['assets']['current']['cash_and_equivalents']), 'المسحوبات البنكية:', format_currency(balance_sheet['liabilities']['current']['accounts_payable'])],
            ['المديونات:', format_currency(balance_sheet['assets']['current']['accounts_receivable']), 'القروض قصيرة الأجل:', format_currency(balance_sheet['liabilities']['current']['short_term_loans'])],
            ['المخزونات:', format_currency(balance_sheet['assets']['current']['inventory']), 'جزء من القروض طويلة الأجل:', format_currency(balance_sheet['liabilities']['current']['current_portion_of_long_term_debt'])],
            ['المصروفات المدفوعة مقدماً:', format_currency(balance_sheet['assets']['current']['prepaid_expenses']), '', ''],
            ['إجمالي الأصول المتداولة:', format_currency(balance_sheet['assets']['current']['total']), 'إجمالي الالتزامات المتداولة:', format_currency(balance_sheet['liabilities']['current']['total'])],
            ['', '', '', ''],
            ['الأصول غير المتداولة:', '', 'الالتزامات غير المتداولة:', ''],
            ['الممتلكات والمعدات:', format_currency(balance_sheet['assets']['non_current']['property_plant_equipment']), 'القروض طويلة الأجل:', format_currency(balance_sheet['liabilities']['non_current']['long_term_loans'])],
            ['الملكية الفكرية:', format_currency(balance_sheet['assets']['non_current']['intangible_assets']), 'الالتزامات المؤجلة:', format_currency(balance_sheet['liabilities']['non_current']['deferred_tax_liabilities'])],
            ['الاستثمارات:', format_currency(balance_sheet['assets']['non_current']['investments']), '', ''],
            ['إجمالي الأصول غير المتداولة:', format_currency(balance_sheet['assets']['non_current']['total']), 'إجمالي الالتزامات غير المتداولة:', format_currency(balance_sheet['liabilities']['non_current']['total'])],
            ['', '', '', ''],
            ['', 'إجمالي الالتزامات:', format_currency(balance_sheet['liabilities']['total']), ''],
            ['', '', '', ''],
            ['حقوق الملكية:', '', '', ''],
            ['رأس المال:', format_currency(balance_sheet['equity']['share_capital']), '', ''],
            ['الأرباح المتراكمة:', format_currency(balance_sheet['equity']['retained_earnings']), '', ''],
            ['', 'إجمالي حقوق الملكية:', format_currency(balance_sheet['equity']['total']), ''],
            ['', '', '', ''],
            ['إجمالي الأصول:', format_currency(balance_sheet['assets']['total']), 'إجمالي الالتزامات والحقوق:', format_currency(balance_sheet['liabilities']['total'] + balance_sheet['equity']['total'])]
        ]

    elif report_type == 'income_statement':
        title = Paragraph("قائمة الدخل", title_style)
        elements.append(title)

        # توليد بيانات قائمة الدخل
        income_statement = generate_income_statement(date.today().year, date.today().month)

        # إنشاء جدول قائمة الدخل
        data = [
            ['الإيرادات', ''],
            ['إيرادات المبيعات:', format_currency(income_statement['revenues']['sales_revenue'])],
            ['إيرادات الخدمات:', format_currency(income_statement['revenues']['service_revenue'])],
            ['إيرادات أخرى:', format_currency(income_statement['revenues']['other_revenue'])],
            ['', 'إجمالي الإيرادات:', format_currency(income_statement['revenues']['total'])],
            ['', '', ''],
            ['التكاليف:', ''],
            ['تكلفة المبيعات:', format_currency(income_statement['costs']['cost_of_sales'])],
            ['', 'صافي الربح من المبيعات:', format_currency(income_statement['costs']['gross_profit'])],
            ['', '', ''],
            ['المصروفات التشغيلية:', ''],
            ['مصروفات البيع:', format_currency(income_statement['operating_expenses']['selling_expenses'])],
            ['مصروفات الإدارة:', format_currency(income_statement['operating_expenses']['administrative_expenses'])],
            ['مصروفات الإهلاك:', format_currency(income_statement['operating_expenses']['depreciation_expense'])],
            ['مصروفات الاستهلاك:', format_currency(income_statement['operating_expenses']['amortization_expense'])],
            ['', 'إجمالي المصروفات التشغيلية:', format_currency(income_statement['operating_expenses']['total'])],
            ['', '', ''],
            ['صافي الدخل من العمليات:', format_currency(income_statement['operating_income'])],
            ['', '', ''],
            ['الدخل غير التشغيلي:', ''],
            ['إيرادات فائدة:', format_currency(income_statement['non_operating']['income']['interest_income'])],
            ['إيرادات أخرى:', format_currency(income_statement['non_operating']['income']['other_income'])],
            ['', 'إجمالي الإيرادات غير التشغيلية:', format_currency(income_statement['non_operating']['income']['total'])],
            ['', '', ''],
            ['مصروفات فائدة:', format_currency(income_statement['non_operating']['expenses']['interest_expense'])],
            ['مصروفات أخرى:', format_currency(income_statement['non_operating']['expenses']['other_expenses'])],
            ['', 'إجمالي المصروفات غير التشغيلية:', format_currency(income_statement['non_operating']['expenses']['total'])],
            ['', '', ''],
            ['صافي الربح قبل الضريبة:', format_currency(income_statement['profit_before_tax'])],
            ['الضرائب على الدخل:', format_currency(income_statement['tax']['income_tax_expense'])],
            ['', '', ''],
            ['صافي الربح:', format_currency(income_statement['net_income'])]
        ]

    elif report_type == 'cash_flow_statement':
        title = Paragraph("قائمة التدفقات النقدية", title_style)
        elements.append(title)

        # توليد بيانات قائمة التدفقات النقدية
        cash_flow_statement = generate_cash_flow_statement(date.today().year, date.today().month)

        # إنشاء جدول قائمة التدفقات النقدية
        data = [
            ['التدفقات النقدية من العمليات:', ''],
            ['صافي الربح:', format_currency(cash_flow_statement['operating']['net_income'])],
            ['إهلاك واستهلاك:', format_currency(cash_flow_statement['operating']['depreciation_and_amortization'])],
            ['تغير رأس المال العامل:', format_currency(cash_flow_statement['operating']['changes_in_working_capital'])],
            ['', 'إجمالي التدفقات النقدية من العمليات:', format_currency(cash_flow_statement['operating']['total'])],
            ['', '', ''],
            ['التدفقات النقدية من الاستثمار:', ''],
            ['مصروفات رأس المال:', format_currency(cash_flow_statement['investing']['capital_expenditures'])],
            ['عائدات بيع الأصول:', format_currency(cash_flow_statement['investing']['proceeds_from_asset_sales'])],
            ['استحواذات:', format_currency(cash_flow_statement['investing']['acquisitions'])],
            ['', 'إجمالي التدفقات النقدية من الاستثمار:', format_currency(cash_flow_statement['investing']['total'])],
            ['', '', ''],
            ['التدفقات النقدية من التمويل:', ''],
            ['إيرادات القروض:', format_currency(cash_flow_statement['financing']['proceeds_from_debt'])],
            ['سداد القروض:', format_currency(cash_flow_statement['financing']['debt_repayments'])],
            ['إيرادات رأس المال:', format_currency(cash_flow_statement['financing']['proceeds_from_equity'])],
            ['توزيعات أرباح:', format_currency(cash_flow_statement['financing']['dividends_paid'])],
            ['', 'إجمالي التدفقات النقدية من التمويل:', format_currency(cash_flow_statement['financing']['total'])],
            ['', '', ''],
            ['التغير صافي في النقد:', format_currency(cash_flow_statement['net_change'])],
            ['النقد في بداية الفترة:', format_currency(cash_flow_statement['cash_at_beginning'])],
            ['النقد في نهاية الفترة:', format_currency(cash_flow_statement['cash_at_end'])]
        ]

    # إضافة الجدول إلى العناصر
    table = Table(data, colWidths=[2.5*inch, 1.5*inch, 2.5*inch, 1.5*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    elements.append(table)

    # إضافة تاريخ التقرير
    date_style = ParagraphStyle(
        'Date',
        parent=styles['Normal'],
        fontSize=10,
        alignment=1  # center
    )
    report_date = Paragraph(f"تاريخ التقرير: {date.today().strftime('%Y-%m-%d')}", date_style)
    elements.append(Spacer(1, 20))
    elements.append(report_date)

    # إنشاء المستند
    doc.build(elements)

    # الحصول على محتوى PDF
    pdf_content = buffer.getvalue()
    buffer.close()

    # إرسال PDF للمستخدم
    return send_file(
        io.BytesIO(pdf_content),
        mimetype='application/pdf',
        as_attachment=True,
        download_name=f'{report_type}_report_{date.today().strftime("%Y%m%d")}.pdf'
    )

def format_currency(value):
    # تنسيق القيمة كعملة
    return f"{value:,.2f}"
