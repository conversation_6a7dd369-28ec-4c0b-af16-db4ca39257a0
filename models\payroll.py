
from app import db
from datetime import datetime, date
from decimal import Decimal
import uuid

class Employee(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    employee_number = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    hire_date = db.Column(db.Date, nullable=False)
    termination_date = db.Column(db.Date, nullable=True)
    salary = db.Column(db.Numeric(15, 2), nullable=False)
    pay_grade = db.Column(db.String(20))
    department_id = db.Column(db.Integer, db.<PERSON><PERSON><PERSON>('department.id'), nullable=True)
    position_id = db.Column(db.Integer, db.ForeignKey('position.id'), nullable=True)
    bank_account = db.Column(db.String(50))
    bank_name = db.Column(db.String(100))
    tax_id = db.Column(db.String(50))
    social_security_number = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    department = db.relationship('Department')
    position = db.relationship('Position')
    payroll_records = db.relationship('PayrollRecord', backref='employee', lazy='dynamic')
    allowances = db.relationship('EmployeeAllowance', backref='employee', lazy='dynamic')
    deductions = db.relationship('EmployeeDeduction', backref='employee', lazy='dynamic')

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __repr__(self):
        return f'<Employee {self.employee_number}: {self.get_full_name()}>'

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    manager_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    manager = db.relationship('Employee', backref='managed_department')
    employees = db.relationship('Employee', backref='department')

    def __repr__(self):
        return f'<Department {self.name}>'

class Position(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    grade = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employees = db.relationship('Employee', backref='position')

    def __repr__(self):
        return f'<Position {self.name}>'

class AllowanceType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    is_percentage = db.Column(db.Boolean, default=False)
    is_taxable = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    allowances = db.relationship('Allowance', backref='allowance_type', lazy='dynamic')
    employee_allowances = db.relationship('EmployeeAllowance', backref='allowance_type', lazy='dynamic')

    def __repr__(self):
        return f'<AllowanceType {self.name}>'

class DeductionType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    is_percentage = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    deductions = db.relationship('Deduction', backref='deduction_type', lazy='dynamic')
    employee_deductions = db.relationship('EmployeeDeduction', backref='deduction_type', lazy='dynamic')

    def __repr__(self):
        return f'<DeductionType {self.name}>'

class Allowance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    allowance_type_id = db.Column(db.Integer, db.ForeignKey('allowance_type.id'), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    allowance_type = db.relationship('AllowanceType')

    def __repr__(self):
        return f'<Allowance {self.id}>'

class Deduction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    deduction_type_id = db.Column(db.Integer, db.ForeignKey('deduction_type.id'), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    deduction_type = db.relationship('DeductionType')

    def __repr__(self):
        return f'<Deduction {self.id}>'

class EmployeeAllowance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=False)
    allowance_type_id = db.Column(db.Integer, db.ForeignKey('allowance_type.id'), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee')
    allowance_type = db.relationship('AllowanceType')

    def __repr__(self):
        return f'<EmployeeAllowance {self.id}>'

class EmployeeDeduction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=False)
    deduction_type_id = db.Column(db.Integer, db.ForeignKey('deduction_type.id'), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee')
    deduction_type = db.relationship('DeductionType')

    def __repr__(self):
        return f'<EmployeeDeduction {self.id}>'

class LeaveType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    days_per_year = db.Column(db.Integer, nullable=False)
    is_paid = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    leaves = db.relationship('Leave', backref='leave_type', lazy='dynamic')

    def __repr__(self):
        return f'<LeaveType {self.name}>'

class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=False)
    leave_type_id = db.Column(db.Integer, db.ForeignKey('leave_type.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    approved_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee')
    leave_type = db.relationship('LeaveType')
    approved_by = db.relationship('User')

    def __repr__(self):
        return f'<Leave {self.id}>'

class PayrollPeriod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, processing, completed, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    payroll_records = db.relationship('PayrollRecord', backref='payroll_period', lazy='dynamic')

    def __repr__(self):
        return f'<PayrollPeriod {self.name}>'

class PayrollRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=False)
    payroll_period_id = db.Column(db.Integer, db.ForeignKey('payroll_period.id'), nullable=False)
    basic_salary = db.Column(db.Numeric(15, 2), nullable=False)
    allowances = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    deductions = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    tax_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    net_pay = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.String(20), default='bank')  # cash, bank, cheque
    payment_reference = db.Column(db.String(50))
    status = db.Column(db.String(20), default='draft')  # draft, processed, paid
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee')
    payroll_period = db.relationship('PayrollPeriod')

    def __repr__(self):
        return f'<PayrollRecord {self.id}>'

class TaxRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    min_income = db.Column(db.Numeric(15, 2), nullable=False)
    max_income = db.Column(db.Numeric(15, 2), nullable=True)
    tax_rate = db.Column(db.Numeric(10, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<TaxRule {self.name}>'
