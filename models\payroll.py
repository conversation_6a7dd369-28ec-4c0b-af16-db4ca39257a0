# models/payroll_simple.py
from datetime import datetime, date
from decimal import Decimal
import uuid

# تأجيل استيراد db حتى يتم استيراد النماذج في app.py
db = None

class Employee:
    pass

class PayrollRecord:
    pass

def init_payroll_models(database):
    """تهيئة نماذج الرواتب مع كائن قاعدة البيانات"""
    global db
    db = database

    # إضافة حقول Employee
    Employee.id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    Employee.employee_number = db.Column(db.String(20), unique=True, nullable=False)
    Employee.first_name = db.Column(db.String(50), nullable=False)
    Employee.last_name = db.Column(db.String(50), nullable=False)
    Employee.email = db.Column(db.String(120))
    Employee.phone = db.Column(db.String(20))
    Employee.address = db.Column(db.Text)
    Employee.hire_date = db.Column(db.Date, nullable=False)
    Employee.salary = db.Column(db.Numeric(15, 2), nullable=False)
    Employee.is_active = db.Column(db.Boolean, default=True)
    Employee.created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # إضافة حقول PayrollRecord
    PayrollRecord.id = db.Column(db.Integer, primary_key=True)
    PayrollRecord.employee_id = db.Column(db.String(36), db.ForeignKey('employee.id'), nullable=False)
    PayrollRecord.basic_salary = db.Column(db.Numeric(15, 2), nullable=False)
    PayrollRecord.allowances = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    PayrollRecord.deductions = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    PayrollRecord.net_salary = db.Column(db.Numeric(15, 2), nullable=False)
    PayrollRecord.payment_date = db.Column(db.Date, nullable=True)
    PayrollRecord.created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    PayrollRecord.employee = db.relationship('Employee')

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __repr__(self):
        return f'<Employee {self.employee_number}: {self.get_full_name()}>'
    
    def payroll_repr(self):
        return f'<PayrollRecord {self.employee_id}>'
    
    Employee.get_full_name = get_full_name
    Employee.__repr__ = __repr__
    PayrollRecord.__repr__ = payroll_repr
