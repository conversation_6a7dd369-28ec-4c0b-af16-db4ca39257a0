
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
from models import User
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        user = User.query.filter_by(username=username).first()

        if not user:
            flash('الرجاء التحقق من اسم المستخدم أو كلمة المرور', 'danger')
            return render_template('auth/login.html')

        if not user.check_password(password):
            flash('الرجاء التحقق من اسم المستخدم أو كلمة المرور', 'danger')
            return render_template('auth/login.html')

        login_user(user, remember=remember)
        user.last_login = datetime.utcnow()
        db.session.commit()

        next_page = request.args.get('next')
        return redirect(next_page) if next_page else redirect(url_for('dashboard'))

    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        user = User.query.filter_by(username=username).first()
        if user:
            flash('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر', 'danger')
            return render_template('auth/register.html')

        user = User.query.filter_by(email=email).first()
        if user:
            flash('البريد الإلكتروني مسجل بالفعل، يرجى استخدام بريد آخر', 'danger')
            return render_template('auth/register.html')

        if password != confirm_password:
            flash('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'danger')
            return render_template('auth/register.html')

        new_user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            password_hash=generate_password_hash(password, method='sha256')
        )

        db.session.add(new_user)
        db.session.commit()

        flash('تم إنشاء حسابك بنجاح، يمكنك الآن تسجيل الدخول', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html')

@auth_bp.route('/profile')
@login_required
def profile():
    return render_template('auth/profile.html')
