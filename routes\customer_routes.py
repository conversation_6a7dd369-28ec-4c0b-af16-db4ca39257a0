
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import Customer, Invoice, InvoiceItem, Payment
from decimal import Decimal
from datetime import datetime

customer_bp = Blueprint('customers', __name__)

@customer_bp.route('/')
@login_required
def customers():
    # الحصول على جميع العملاء
    customers = Customer.query.all()
    return render_template('customers/customers.html', customers=customers)

@customer_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_customer():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        code = request.form.get('code')
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        tax_id = request.form.get('tax_id')
        credit_limit = Decimal(request.form.get('credit_limit', 0))

        # التحقق من عدم تكرار كود العميل
        existing_customer = Customer.query.filter_by(code=code).first()
        if existing_customer:
            flash('كود العميل موجود بالفعل', 'danger')
            return render_template('customers/new_customer.html')

        # إنشاء العميل الجديد
        new_customer = Customer(
            code=code,
            name=name,
            contact_person=contact_person,
            email=email,
            phone=phone,
            address=address,
            tax_id=tax_id,
            credit_limit=credit_limit
        )

        db.session.add(new_customer)
        db.session.commit()

        flash('تم إنشاء العميل بنجاح', 'success')
        return redirect(url_for('customers.customers'))

    return render_template('customers/new_customer.html')

@customer_bp.route('/<customer_id>')
@login_required
def customer_detail(customer_id):
    # الحصول على تفاصيل العميل
    customer = Customer.query.get_or_404(customer_id)

    # الحصول على الفواتير والمدفوعات المرتبطة بالعميل
    invoices = Invoice.query.filter_by(customer_id=customer_id).order_by(Invoice.date.desc()).all()
    payments = Payment.query.filter_by(customer_id=customer_id).order_by(Payment.date.desc()).all()

    # حساب إجمالي المبالغ
    total_invoices = sum(inv.total_amount for inv in invoices if inv.status == 'open')
    total_payments = sum(pay.amount for pay in payments)
    balance = total_invoices - total_payments

    return render_template('customers/customer_detail.html', customer=customer, invoices=invoices, payments=payments, balance=balance)

@customer_bp.route('/<customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        # تحديث بيانات العميل
        customer.code = request.form.get('code')
        customer.name = request.form.get('name')
        customer.contact_person = request.form.get('contact_person')
        customer.email = request.form.get('email')
        customer.phone = request.form.get('phone')
        customer.address = request.form.get('address')
        customer.tax_id = request.form.get('tax_id')
        customer.credit_limit = Decimal(request.form.get('credit_limit', 0))

        db.session.commit()

        flash('تم تحديث بيانات العميل بنجاح', 'success')
        return redirect(url_for('customers.customer_detail', customer_id=customer_id))

    return render_template('customers/edit_customer.html', customer=customer)

@customer_bp.route('/<customer_id>/delete', methods=['POST'])
@login_required
def delete_customer(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    # التحقق من وجود فواتير أو مدفوعات مرتبطة بالعميل
    if customer.invoices.count() > 0 or customer.payments.count() > 0:
        flash('لا يمكن حذف العميل لأن لديه فواتير أو مدفوعات مسجلة', 'danger')
        return redirect(url_for('customers.customer_detail', customer_id=customer_id))

    db.session.delete(customer)
    db.session.commit()

    flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers.customers'))

@customer_bp.route('/<customer_id>/invoices')
@login_required
def customer_invoices(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    # الحصول على جميع فواتير العميل
    invoices = Invoice.query.filter_by(customer_id=customer_id).order_by(Invoice.date.desc()).all()

    return render_template('customer_invoices.html', customer=customer, invoices=invoices)

@customer_bp.route('/<customer_id>/new-invoice', methods=['GET', 'POST'])
@login_required
def new_customer_invoice(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        due_date = datetime.strptime(request.form.get('due_date'), '%Y-%m-%d').date()

        # إنشاء الفاتورة
        invoice = Invoice(
            customer_id=customer_id,
            date=date,
            due_date=due_date,
            total_amount=Decimal('0.00'),  # سيتم تحديثه لاحقًا
            tax_amount=Decimal('0.00'),
            discount_amount=Decimal('0.00'),
            status='draft'
        )
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID الفاتورة

        # جمع بنود الفاتورة
        total_amount = Decimal('0.00')
        tax_amount = Decimal('0.00')
        discount_amount = Decimal(request.form.get('discount_amount', 0))

        for key, value in request.form.items():
            if key.startswith('item_description_'):
                index = key.split('_')[2]
                description = value
                quantity = Decimal(request.form.get(f'item_quantity_{index}', 0))
                unit_price = Decimal(request.form.get(f'item_unit_price_{index}', 0))
                tax_rate = Decimal(request.form.get(f'item_tax_rate_{index}', 0))

                # حساب إجمالي البند
                line_total = quantity * unit_price
                line_tax = line_total * (tax_rate / 100)

                # إنشاء بند الفاتورة
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    description=description,
                    quantity=quantity,
                    unit_price=unit_price,
                    tax_rate=tax_rate
                )
                db.session.add(invoice_item)

                # تحديث المجاميع
                total_amount += line_total
                tax_amount += line_tax

        # تطبيق الخصم
        if discount_amount > 0:
            # إذا كان الخصم نسبة مئوية
            if discount_amount <= 100:
                discount_amount = total_amount * (discount_amount / 100)
            total_amount -= discount_amount

        # تحديث الفاتورة بالمجاميع النهائية
        invoice.total_amount = total_amount
        invoice.tax_amount = tax_amount
        invoice.discount_amount = discount_amount
        db.session.commit()

        flash('تم إنشاء الفاتورة بنجاح', 'success')
        return redirect(url_for('customers.customer_invoices', customer_id=customer_id))

    return render_template('customers/new_invoice.html', customer=customer)

@customer_bp.route('/<customer_id>/payments')
@login_required
def customer_payments(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    # الحصول على جميع المدفوعات المرتبطة بالعميل
    payments = Payment.query.filter_by(customer_id=customer_id).order_by(Payment.date.desc()).all()

    return render_template('customer_payments.html', customer=customer, payments=payments)

@customer_bp.route('/<customer_id>/new-payment', methods=['GET', 'POST'])
@login_required
def new_customer_payment(customer_id):
    # الحصول على العميل
    customer = Customer.query.get_or_404(customer_id)

    # الحصول على الفواتير المفتوحة للعميل
    open_invoices = Invoice.query.filter_by(customer_id=customer_id, status='open').all()

    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        amount = Decimal(request.form.get('amount'))
        payment_method = request.form.get('payment_method')
        reference_number = request.form.get('reference_number')
        notes = request.form.get('notes')

        # إنشاء المدفوعة
        payment = Payment(
            customer_id=customer_id,
            date=date,
            amount=amount,
            payment_method=payment_method,
            reference_number=reference_number,
            notes=notes
        )
        db.session.add(payment)
        db.session.flush()  # للحصول على ID المدفوعة

        # تحديث الفاتورة المدفوعة إذا تم تحديد فاتورة محددة
        invoice_id = request.form.get('invoice_id')
        if invoice_id:
            invoice = Invoice.query.get(invoice_id)
            if invoice:
                # تحديث المبلغ المتبقي للفاتورة
                remaining_amount = invoice.get_remaining_amount()
                payment_amount = min(amount, remaining_amount)

                # تحديث المدفوعة بالفعل
                payment.invoice_id = invoice.id
                payment.amount = payment_amount

                # تحديث حالة الفاتورة إذا تم دفع كامل المبلغ
                if remaining_amount - payment_amount <= 0:
                    invoice.status = 'paid'

                amount -= payment_amount

                # إذا كان هناك مبلغ إضافي، يمكن استخدامه لفاتورة أخرى أو إرجاعه للعميل
                if amount > 0:
                    flash(f'تم دفع {payment_amount} للفاتورة {invoice.invoice_number}، و {amount} متبقي', 'info')

        db.session.commit()

        flash('تم تسجيل المدفوعة بنجاح', 'success')
        return redirect(url_for('customers.customer_payments', customer_id=customer_id))

    return render_template('customers/new_payment.html', customer=customer, open_invoices=open_invoices)
