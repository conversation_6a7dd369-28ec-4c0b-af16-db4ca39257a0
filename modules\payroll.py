# payroll.py
class PayrollSystem:
    def __init__(self):
        self.employees = {}
        self.orders = {}
        self.items = {}

    def calculate_salary(self, employee_id, base, bonus=0, deductions=0, tax_rate=0.15):
        """
        计算员工净工资
        参数:
            employee_id: 员工ID
            base: 基本工资
            bonus: 奖金 (默认为0)
            deductions: 扣除项 (默认为0)
            tax_rate: 税率 (默认为15%)
        返回:
            net: 净工资
        """
        # 计算应税工资
        taxable_income = base + bonus - deductions
        # 计算税额
        tax = taxable_income * tax_rate
        # 计算净工资
        net = taxable_income - tax
        
        # 保存员工薪资信息
        self.employees[employee_id] = {
            'base': base,
            'bonus': bonus,
            'deductions': deductions,
            'tax': tax,
            'net': net
        }
        
        print(f"员工 {employee_id} 的工资明细:")
        print(f"基本工资: {base}")
        print(f"奖金: {bonus}")
        print(f"扣除项: {deductions}")
        print(f"税额: {tax}")
        print(f"净工资: {net}")
        
        return net

    def generate_payslip(self, employee_id):
        """
        生成员工工资单
        参数:
            employee_id: 员工ID
        """
        if employee_id not in self.employees:
            print(f"错误: 未找到员工 {employee_id} 的信息")
            return None
            
        employee = self.employees[employee_id]
        print(f"\n=== 工资单 ===")
        print(f"员工ID: {employee_id}")
        print(f"基本工资: {employee['base']}")
        print(f"奖金: {employee['bonus']}")
        print(f"扣除项: {employee['deductions']}")
        print(f"税额: {employee['tax']}")
        print(f"净工资: {employee['net']}")
        print("============\n")
        
        return employee

    def add_order(self, order_id, employee_id, amount, description):
        """
        添加订单信息
        参数:
            order_id: 订单ID
            employee_id: 关联的员工ID
            amount: 金额
            description: 描述
        """
        self.orders[order_id] = {
            'employee_id': employee_id,
            'amount': amount,
            'description': description
        }
        print(f"已添加订单 {order_id}")

    def add_item(self, item_id, name, cost, category):
        """
        添加项目信息
        参数:
            item_id: 项目ID
            name: 名称
            cost: 成本
            category: 类别
        """
        self.items[item_id] = {
            'name': name,
            'cost': cost,
            'category': category
        }
        print(f"已添加项目 {item_id}")

    def generate_report(self, employee_id):
        """
        生成员工综合报告
        参数:
            employee_id: 员工ID
        """
        if employee_id not in self.employees:
            print(f"错误: 未找到员工 {employee_id} 的信息")
            return None
            
        print(f"\n=== 员工综合报告 ===")
        # 打印薪资信息
        self.generate_payslip(employee_id)
        
        # 打印相关订单
        print("\n相关订单:")
        for order_id, order in self.orders.items():
            if order['employee_id'] == employee_id:
                print(f"订单 {order_id}:")
                print(f"  金额: {order['amount']}")
                print(f"  描述: {order['description']}")
        
        print("\n===============\n")
        return True

    def search_item(self, item_id):
        """
        搜索项目信息
        参数:
            item_id: 项目ID
        """
        if item_id not in self.items:
            print(f"错误: 未找到项目 {item_id} 的信息")
            return None
            
        item = self.items[item_id]
        print(f"\n==== 项目信息 ====")
        print(f"项目ID: {item_id}")
        print(f"名称: {item['name']}")
        print(f"成本: {item['cost']}")
        print(f"类别: {item['category']}")
        print("===============\n")
        
        return item 
    def update_item(self, item_id, name=None, cost=None, category=None):
        """ 
        تحديث معلومات العنصر
        المعلمات:
            item_id: معرف العنصر
            name: الاسم (اختياري)
            cost: التكلفة (اختياري)
            category: الفئة (اختياري)

        """
        if item_id not in self.items:
            print(f"错误: 未找到项目 {item_id} 的信息")
            return None
            
        item = self.items[item_id]
        if name is not None:
            item['name'] = name
        if cost is not None:
            item['cost'] = cost
        if category is not None:
            item['category'] = category
        print(f"已更新项目 {item_id}")
        return item
    def delete_item(self, item_id):
        """
        حذف عنصر
        المعلمات:
            item_id: معرف العنصر
        """
        if item_id not in self.items:
            print(f"错误: 未找到项目 {item_id} 的信息")
            return None
            
        del self.items[item_id]
        print(f"已删除项目 {item_id}")
        return True     
    def list_items(self):
        """ 
        قائمة العناصر
        """
        print("\n=== قائمة العناصر ===")
        for item_id, item in self.items.items():
            print(f"ID: {item_id}")
            print(f"اسم: {item['name']}")
            print(f"تكلفة: {item['cost']}")
            print(f"فئة: {item['category']}")
            print("===============\n")
        return True    
    def list_orders(self):
        """ 
        قائمة الأوامر
        """
        print("\n=== قائمة الأوامر ===")
        for order_id, order in self.orders.items():
            print(f"ID: {order_id}")
            print(f"معرف الموظف: {order['employee_id']}")
            print(f"المبلغ: {order['amount']}")
            print(f"الوصف: {order['description']}")
            print("===============\n")
        return True 
    def list_employees(self):
        """ 
        قائمة الموظفين
        """
        print("\n=== قائمة الموظفين ===")
        for employee_id, employee in self.employees.items():
            print(f"ID: {employee_id}")
            print(f"اسم: {employee['name']}")
            print(f"رقم الجوال: {employee['phone_number']}")
            print("===============\n")
        return True 
    def add_employee(self, employee_id, name, phone_number):
        """ 
        اضافة موظف
        المعلمات:
            employee_id: معرف الموظف
            name: الاسم
            phone_number: رقم الجوال
        """
        self.employees[employee_id] = {
            'name': name,
            'phone_number': phone_number
        }
        print(f"تم اضافة الموظف {employee_id}")
        return True 
    def update_employee(self, employee_id, name=None, phone_number=None):
        """ 
        تحديث معلومات الموظف
        المعلمات:
            employee_id: معرف الموظف
            name: الاسم (اختياري)
            phone_number: رقم الجوال (اختياري)
        """
        if employee_id not in self.employees:
            print(f"错误: 未找到 الموظف {employee_id} في قائمة الموظفين")
            return None
            
        employee = self.employees[employee_id]
        if name is not None:
            employee['name'] = name
        if phone_number is not None:
            employee['phone_number'] = phone_number
        print(f"تم تحديث الموظف {employee_id}")
        return employee 
    def delete_employee(self, employee_id):
        """
        حذف موظف
        المعلمات:
            employee_id: معرف الموظف
        """
        if employee_id not in self.employees:
            print(f"错误: 未找到 الموظف {employee_id} في قائمة الموظفين")
            return None
            
        del self.employees[employee_id]
        print(f"تم حذف الموظف {employee_id}")
        return True
    def search_employee(self, employee_id):
        """
        بحث عن موظف
        المعلمات:
            employee_id: معرف الموظف
        """
        if employee_id not in self.employees:
            print(f"错误: 未找到 الموظف {employee_id} في قائمة الموظفين")
            return None
            
        return self.employees[employee_id]  
    def employee_report(self, employee_id):
        """
        تقرير الموظف
        المعلمات:
            employee_id: معرف الموظف
        """
        print(f"تقرير الموظف {employee_id}")
        return True
    def item_report(self, item_id):
        """
        تقرير الصنف
        المعلمات:
            item_id: معرف الصنف
        """
        print(f"تقرير الصنف {item_id}")
        return True
    def order_report(self, order_id):
        """
        تقرير الطلب
        المعلمات:
            order_id: معرف الطلب
        """
        print(f"تقرير الطلب {order_id}")
        return True 
    def payroll_summary(self):
        """
        ملخص الرواتب
        """
        print("ملخص الرواتب")
        return True
    def tax_report(self):
        """
        تقرير الضريبة
        """
        print("تقرير الضريبة")
        return True
    def bonus_report(self):
        """
        تقرير البونص
        """
        print("تقرير البونص")
        return True
    def deduction_report(self):
        """
        تقرير الخصومات
        """
        print("تقرير الخصومات")
        return True 
    def generate_invoice(self, order_id):
        """
        توليد فاتورة
        المعلمات:
            order_id: معرف الطلب
        """
        print(f"توليد فاتورة للطلب {order_id}")
        return True
    def send_payslip(self, employee_id):
        """
        ارسال رسالة الرواتب
        المعلمات:
            employee_id: معرف الموظف
        """
        print(f"ارسال رسالة الرواتب للموظف {employee_id}")
        return True 
    def backup_data(self):
        """
        نسخة احتياطية للبيانات
        """
        print("نسخة احتياطية للبيانات")
        return True
    def restore_data(self):
        """
        استعادة البيانات
        """
        print("استعادة البيانات")
        return True
    def export_report(self, report_type):
        """
        تصدير التقارير
        المعلمات:
            report_type: نوع التقرير
        """
        print(f"تصدير التقرير {report_type}")
        return True
    def import_data(self, data_type):
        """
        استيراد البيانات
        المعلمات:
            data_type: نوع البيانات
        """
        print(f"استيراد البيانات {data_type}")
        return True
    def audit_log(self):
        """
        سجل المراقبة
        """
        print("سجل المراقبة")
        return True 
    def system_settings(self):
        """
        اعدادات النظام
        """
        print("اعدادات النظام")
        return True    
    
def order_report(self, order_id):
    """
    Generate order report
    Parameters:
        order_id: The ID of the order
    """
    print(f"Order report {order_id}")
    return True



def item_report(self, item_id):
        """
        تقرير الصنف
        المعلمات:
            item_id: معرف الصنف
        """
        print(f"تقرير الصنف {item_id}")
        return True 
def set_item_discount(self, item_id, discount):
        """
        تعيين خصم للصنف
        المعلمات:
            item_id: معرف الصنف
            discount: نسبة الخصم
        """
        print(f"تعيين خصم للصنف {item_id} بنسبة {discount}%")
        return True
def calculate_overtime(self, employee_id, hours, rate):
        """
        حساب الوقت المضاف
        المعلمات:
            employee_id: معرف الموظف
            hours: عدد ساعات الوقت المضاف
            rate: سعر الوقت المضاف
        """
        print(f"حساب الوقت المضاف للموظف {employee_id} بسعر {rate} لساعة")
        return True
def schedule_payment(self, employee_id, date):
        """
        جدولة المدفوعات
        المعلمات:
            employee_id: معرف الموظف
            date: تاريخ المدفوعات
        """
        print(f"جدولة المدفوعات للموظف {employee_id} لتاريخ {date}")
        return True
def track_attendance(self, employee_id, date, status):
        """
        تتبع الحضور
        المعلمات:
            employee_id: معرف الموظف
            date: تاريخ الحضور
            status: حالة الحضور
        """
        print(f"تتبع الحضور للموظف {employee_id} لتاريخ {date} بحالة {status}")
        return True
def leave_management(self, employee_id, start_date, end_date, reason):
        """
        ادارة الاجازات
        المعلمات:
            employee_id: معرف الموظف
            start_date: تاريخ البدء
            end_date: تاريخ الانتهاء
            reason: سبب الاجازة
        """
        print(f"ادارة الاجازات للموظف {employee_id} من تاريخ {start_date} إلى {end_date} بسبب {reason}")
        return True
def performance_review(self, employee_id, review_date, comments):
        """
        تقييم النشاط
        المعلمات:
            employee_id: معرف الموظف
            review_date: تاريخ التقييم
            comments: تعليقات
        """
        print(f"تقييم النشاط للموظف {employee_id} لتاريخ {review_date} بتعليقات {comments}")
        return True
def training_management(self, employee_id, training_name, date):
        """
        ادارة التدريب
        المعلمات:
            employee_id: معرف الموظف
            training_name: اسم التدريب
            date: تاريخ التدريب
        """
        print(f"ادارة التدريب للموظف {employee_id} باسم {training_name} لتاريخ {date}")
        return True
def compliance_management(self, regulation, status):
        """
        ادارة الاتفاقية
        المعلمات:
            regulation: الاتفاقية
            status: حالة الاتفاقية
        """
        print(f"ادارة الاتفاقية {regulation} بحالة {status}")
        return True
def employee_benefits(self, employee_id, benefit_type, amount):
        """
        حقوق الموظف
        المعلمات:
            employee_id: معرف الموظف
            benefit_type: نوع الحقوق
            amount: المبلغ
        """
        print(f"حقوق الموظف {employee_id} بنوع {benefit_type} بمبلغ {amount}")
        return True
def retirement_plan(self, employee_id, contribution, plan_type):
        """
        خطة التقاعد
        المعلمات:
            employee_id: معرف الموظف
            contribution: المساهمة
            plan_type: نوع الخطة
        """
        print(f"خطة التقاعد للموظف {employee_id} بمساهمة {contribution} بنوع {plan_type}")
        return True
def health_insurance(self, employee_id, provider, plan_type):
        """
        صحة الموظف
        المعلمات:
            employee_id: معرف الموظف
            provider: المزود
            plan_type: نوع الخطة
        """
        print(f"صحة الموظف {employee_id} من المزود {provider} بنوع {plan_type}")
        return True
def travel_expenses(self, employee_id, amount, description):
        """
        تكاليف السفر
        المعلمات:
            employee_id: معرف الموظف
            amount: المبلغ
            description: الوصف
        """
        print(f"تكاليف السفر للموظف {employee_id} بمبلغ {amount} بوصف {description}")
        return True
def project_management(self, project_id, name, budget):
        """
        ادارة المشروع
        المعلمات:
            project_id: معرف المشروع
            name: اسم المشروع
            budget: البودجة
        """
        print(f"ادارة المشروع {project_id} باسم {name} ببودجة {budget}")
        return True
def client_management(self, client_id, name, contact):
        """
        ادارة العميل
        المعلمات:
            client_id: معرف العميل
            name: اسم العميل
            contact: الاتصال
        """
        print(f"ادارة العميل {client_id} باسم {name} باتصال {contact}")
        return True
def vendor_management(self, vendor_id, name, contact):
        """ 
        ادارة المورد
        المعلمات:
            vendor_id: معرف المورد
            name: اسم المورد
            contact: الاتصال
        """
        print(f"ادارة المورد {vendor_id} باسم {name} باتصال {contact}")
        return True
def inventory_management(self, item_id, quantity, location):
        """
        ادارة المخزن
        المعلمات:
            item_id: معرف العنصر
            quantity: الكمية
            location: الموقع
        """
        print(f"ادارة المخزن للعنصر {item_id} بكمية {quantity} في الموقع {location}")
        return True
def asset_management(self, asset_id, name, value):
        """
        ادارة الأصل
        المعلمات:
            asset_id: معرف الأصل
            name: اسم الأصل
            value: القيمة
        """
        print(f"ادارة الأصل {asset_id} باسم {name} بقيمة {value}")
        return True
def budget_planning(self, department, amount):
        """
        تخطيط البودجة
        المعلمات:
            department: القسم
            amount: المبلغ
        """
        print(f"تخطيط البودجة للقسم {department} بمبلغ {amount}")
        return True
def financial_reporting(self, report_type, period):
        """
        تقارير المالية
        المعلمات:
            report_type: نوع التقرير
            period: الفترة
        """
        print(f"تقارير المالية بنوع {report_type} لفترة {period}")
        return True
def audit_management(self, audit_id, date, findings):
        """
        ادارة المراجعة
        المعلمات:
            audit_id: معرف المراجعة
            date: التاريخ
            findings: النتايج
        """
        print(f"ادارة المراجعة {audit_id} بتاريخ {date} بنتايج {findings}")
        return True
def tax_compliance(self, tax_type, status):
        """
        موافقة الضريبة
        المعلمات:
            tax_type: نوع الضريبة
            status: الحالة
        """
        print(f"موافقة الضريبة بنوع {tax_type} بحالة {status}")
        return True
def legal_compliance(self, law, status):
        """
        موافقة القانون
        المعلمات:
            law: القانون
            status: الحالة
        """
        print(f"موافقة القانون {law} بحالة {status}")
        return True
def generate_report(self, employee_id):
        """
        توليد التقرير
        المعلمات:
            employee_id: معرف الموظف
        """
        print(f"توليد التقرير للموظف {employee_id}")
        return True



    