
from app import db
from datetime import datetime
from decimal import Decimal
import uuid

class InventoryItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('inventory_category.id'), nullable=True)
    unit_id = db.Column(db.Integer, db.<PERSON>ey('inventory_unit.id'), nullable=True)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    purchase_account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=True)
    sales_account_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('account.id'), nullable=True)
    opening_quantity = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    opening_value = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    current_quantity = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    reorder_level = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    reorder_quantity = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    unit_cost = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    selling_price = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    tax_rate = db.Column(db.Numeric(5, 2), default=Decimal('0.00'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    category = db.relationship('InventoryCategory')
    unit = db.relationship('InventoryUnit')
    account = db.relationship('Account', foreign_keys=[account_id])
    purchase_account = db.relationship('Account', foreign_keys=[purchase_account_id])
    sales_account = db.relationship('Account', foreign_keys=[sales_account_id])
    transactions = db.relationship('InventoryTransaction', backref='item', lazy='dynamic')
    purchase_order_items = db.relationship('PurchaseOrderItem', backref='inventory_item', lazy='dynamic')

    def get_average_cost(self):
        # حساب متوسط تكلفة الوحدة
        if self.current_quantity <= 0:
            return Decimal('0.00')

        total_value = self.opening_value
        for transaction in self.transactions.filter_by(transaction_type='purchase').all():
            if transaction.is_posted:
                total_value += transaction.value
        return total_value / self.current_quantity

    def __repr__(self):
        return f'<InventoryItem {self.code}: {self.name}>'

class InventoryCategory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('inventory_category.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    parent = db.relationship('InventoryCategory', remote_side=[id])
    children = db.relationship('InventoryCategory')
    items = db.relationship('InventoryItem', backref='category', lazy='dynamic')

    def __repr__(self):
        return f'<InventoryCategory {self.name}>'

class InventoryUnit(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    symbol = db.Column(db.String(10), nullable=False)

    # العلاقات
    items = db.relationship('InventoryItem', backref='unit', lazy='dynamic')

    def __repr__(self):
        return f'<InventoryUnit {self.name}>'

class InventoryTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(50), unique=True, nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('inventory_item.id'), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # purchase, sale, adjustment, transfer
    quantity = db.Column(db.Numeric(15, 2), nullable=False)
    unit_cost = db.Column(db.Numeric(15, 2), nullable=False)
    value = db.Column(db.Numeric(15, 2), nullable=False)
    reference_id = db.Column(db.String(50), nullable=True)  # ID of related transaction (invoice, PO, etc.)
    notes = db.Column(db.Text)
    is_posted = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    item = db.relationship('InventoryItem')
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<InventoryTransaction {self.reference_number}>'

class StockAdjustment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(50), unique=True, nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('inventory_item.id'), nullable=False)
    current_quantity = db.Column(db.Numeric(15, 2), nullable=False)
    new_quantity = db.Column(db.Numeric(15, 2), nullable=False)
    difference = db.Column(db.Numeric(15, 2), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    notes = db.Column(db.Text)
    is_posted = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    item = db.relationship('InventoryItem')
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<StockAdjustment {self.reference_number}>'

class StockTransfer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(50), unique=True, nullable=False)
    from_location_id = db.Column(db.Integer, db.ForeignKey('inventory_location.id'), nullable=False)
    to_location_id = db.Column(db.Integer, db.ForeignKey('inventory_location.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    notes = db.Column(db.Text)
    is_posted = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    from_location = db.relationship('InventoryLocation', foreign_keys=[from_location_id])
    to_location = db.relationship('InventoryLocation', foreign_keys=[to_location_id])
    created_by = db.relationship('User')
    items = db.relationship('StockTransferItem', backref='stock_transfer', lazy='dynamic')

    def __repr__(self):
        return f'<StockTransfer {self.reference_number}>'

class StockTransferItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    stock_transfer_id = db.Column(db.Integer, db.ForeignKey('stock_transfer.id'), nullable=False)
    item_id = db.Column(db.Integer, db.ForeignKey('inventory_item.id'), nullable=False)
    quantity = db.Column(db.Numeric(15, 2), nullable=False)
    unit_cost = db.Column(db.Numeric(15, 2), nullable=False)
    value = db.Column(db.Numeric(15, 2), nullable=False)

    # العلاقات
    item = db.relationship('InventoryItem')

    def __repr__(self):
        return f'<StockTransferItem {self.id}>'

class InventoryLocation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    address = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    transfers_from = db.relationship('StockTransfer', foreign_keys='StockTransfer.from_location_id')
    transfers_to = db.relationship('StockTransfer', foreign_keys='StockTransfer.to_location_id')

    def __repr__(self):
        return f'<InventoryLocation {self.name}>'
