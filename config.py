# config.py - Flask Configuration
import os

class Config:
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'

    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///accounting.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    REPORTS_FOLDER = 'static/reports'
    BACKUP_FOLDER = 'backups'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

    # Currency settings
    CURRENCY = "EGP"
    CURRENCY_SYMBOL = "ج.م"
    DECIMAL_PLACES = 2

    # Language settings
    LANGUAGE = "ar"
    RTL = True

    # Date formats
    DATE_FORMAT = "%Y-%m-%d"
    DATE_DISPLAY_FORMAT = "%d/%m/%Y"
    TIME_FORMAT = "%H:%M:%S"
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    DATETIME_DISPLAY_FORMAT = "%d/%m/%Y %H:%M:%S"

    # Number formats
    NUMBER_FORMAT = "{:,.2f}"
    THOUSAND_SEPARATOR = ","
    DECIMAL_SEPARATOR = "."

    # Login settings
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 15

    # Invoice settings
    INVOICE_PREFIX = "INV-"
    INVOICE_START_NUMBER = 1
    INVOICE_TEMPLATE = {
        'company_name': 'شركتك',
        'company_address': 'العنوان',
        'company_phone': 'رقم الهاتف',
        'tax_number': 'الرقم الضريبي'
    }

    # Accounting entry types
    ENTRY_TYPES = ["مدين", "دائن"]
    DEFAULT_ENTRY_TYPE = "مدين"

    # Backup settings
    AUTO_BACKUP = True
    BACKUP_INTERVAL_HOURS = 24
    MAX_BACKUPS = 7

    # Report settings
    DEFAULT_REPORT_FORMAT = "pdf"
    EXPORT_FORMATS = ["csv", "xlsx", "pdf"]
    IMPORT_FORMATS = ["csv", "xlsx"]

    # System messages
    MESSAGES = {
        'success': {
            'db_init': 'تم تهيئة قاعدة البيانات بنجاح',
            'entry_created': 'تم تسجيل القيد بنجاح',
            'invoice_issued': 'تم إصدار الفاتورة بنجاح'
        },
        'error': {
            'db_error': 'حدث خطأ في قاعدة البيانات',
            'invalid_amount': 'الرجاء إدخال قيمة رقمية صحيحة للمبلغ',
            'login_failed': 'فشل تسجيل الدخول'
        }
    }


    