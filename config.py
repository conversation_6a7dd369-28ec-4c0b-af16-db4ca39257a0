# config.py
# config.py
# إعدادات قاعدة البيانات
DB_PATH = "accounting.db"
DB_BACKUP_PATH = "backups/accounting_backup.db"

# إعدادات العملة
CURRENCY = "EGP,$,sar,syr"
CURRENCY_SYMBOL = "ج.م, $,ر.س,ل.س"
DECIMAL_PLACES = 2

# إعدادات اللغة
LANGUAGE = "ar"
RTL = True

# إعدادات الواجهة
WINDOW_TITLE = "نظام المحاسبة"
WINDOW_WIDTH = 400
WINDOW_HEIGHT = 500
WINDOW_THEME = "clam"

# إعدادات الألوان
WINDOW_BG_COLOR = "#f0f0f0"
BUTTON_BG_COLOR = "#4CAF50"
BUTTON_FG_COLOR = "#ffffff"
LABEL_FG_COLOR = "#333333"
ENTRY_BG_COLOR = "#ffffff"
ENTRY_FG_COLOR = "#333333"
COMBOBOX_BG_COLOR = "#ffffff"
COMBOBOX_FG_COLOR = "#333333"

# إعدادات المسارات
LOGO_PATH = "assets/logo.png"
REPORTS_PATH = "reports"
BACKUP_PATH = "backups"

# إعدادات المستخدم
DEFAULT_USERNAME = "admin"
DEFAULT_PASSWORD = "admin123"
PASSWORD_SALT = "s@ltStr1ng"

# إعدادات الجلسة
SESSION_TIMEOUT_MINUTES = 30

# إعدادات التاريخ
DATE_FORMAT = "%Y-%m-%d"
DATE_DISPLAY_FORMAT = "%d/%m/%Y"
TIME_FORMAT = "%H:%M:%S"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
DATETIME_DISPLAY_FORMAT = "%d/%m/%Y %H:%M:%S"

# إعدادات الأرقام
NUMBER_FORMAT = "{:,.2f}"
THOUSAND_SEPARATOR = ","
DECIMAL_SEPARATOR = "."

# إعدادات تسجيل الدخول
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION_MINUTES = 15

# إعدادات الفواتير
INVOICE_PREFIX = "INV-"
INVOICE_START_NUMBER = 1
INVOICE_TEMPLATE = {
    'company_name': 'شركتك',
    'company_address': 'العنوان',
    'company_phone': 'رقم الهاتف',
    'tax_number': 'الرقم الضريبي'
}

# إعدادات القيود المحاسبية
ENTRY_TYPES = ["مدين", "دائن"]
DEFAULT_ENTRY_TYPE = "مدين"

# إعدادات النسخ الاحتياطي
AUTO_BACKUP = True
BACKUP_INTERVAL_HOURS = 24
MAX_BACKUPS = 7

# إعدادات التقارير
DEFAULT_REPORT_FORMAT = "pdf"

# إعدادات التصدير والاستيراد
EXPORT_FORMATS = ["csv", "xlsx", "pdf"]
IMPORT_FORMATS = ["csv", "xlsx"]

# رسائل النظام
MESSAGES = {
    'success': {
        'db_init': 'تم تهيئة قاعدة البيانات بنجاح',
        'entry_created': 'تم تسجيل القيد بنجاح',
        'invoice_issued': 'تم إصدار الفاتورة بنجاح'
    },
    'error': {
        'db_error': 'حدث خطأ في قاعدة البيانات',
        'invalid_amount': 'الرجاء إدخال قيمة رقمية صحيحة للمبلغ',
        'login_failed': 'فشل تسجيل الدخول'
    }
}

# إعدادات البريد الإلكتروني
EMAIL_SETTINGS = {
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'smtp_username': '',
    'smtp_password': '',
    'sender_email': '',
    'use_tls': True,
    'timeout': 30,
    'max_retries': 3,
    'email_templates': {
        'invoice': {
            'subject': 'فاتورة جديدة من {company_name}',
            'body': '''عزيزي العميل،

            مرفق لكم فاتورة جديدة بقيمة {amount} {currency}.
            رقم الفاتورة: {invoice_number}
            تاريخ الإصدار: {date}

            مع خالص التقدير،
            {company_name}'''
        },
        'reminder': {
            'subject': 'تذكير بالدفع - فاتورة رقم {invoice_number}',
            'body': '''عزيزي العميل،

            نذكركم بفاتورة مستحقة الدفع بقيمة {amount} {currency}.
            رقم الفاتورة: {invoice_number}
            تاريخ الاستحقاق: {due_date}

            مع خالص التقدير،
            {company_name}'''
        }
    },
    'attachments': {
        'invoice_format': 'pdf',
        'include_company_logo': True,
        'max_attachment_size': 10
    },
    'schedule': {
        'send_invoices_automatically': False,
        'send_reminders': True,
        'reminder_days_before_due': 3,
        'reminder_interval_days': 7
    }
}

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import json
import csv
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from datetime import datetime, timedelta
import threading
import os
import re
import locale
import time
import random
import string
import base64
import sys
import traceback
import subprocess
import shutil
import logging
from logging.handlers import RotatingFileHandler
import 

# Import configurations and custom modules
from config import *
from modules.general_ledger import create_entry
from modules.clients_suppliers import issue_invoice, get_client_names
from database.models import init_db


# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()
handler = RotatingFileHandler("app.log", maxBytes=2000000, backupCount=5)
handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)

# Import configurations and modules
from config import *
from modules.general_ledger import create_entry
from modules.clients_suppliers import issue_invoice, get_client_names
from database.models import init_db

# Set locale
try:
    locale.setlocale(locale.LC_ALL, "ar_EG.UTF-8")
except:
    logger.warning("Failed to set Arabic locale, using default")

class AccountingApp:
    def __init__(self, root):
        try:
            # Initialize database
            self.db = init_db()
            
            # Configure main window
            self.root = root
            self.root.title(WINDOW_TITLE)
            self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
            self.root.resizable(False, False)
            self.root.protocol("WM_DELETE_WINDOW", self.show_exit)
            
            # Set up theme and styles
            self.setup_styles()
            
            # Create main interface
            self.create_menu()
            self.create_main_interface()
            
        except Exception as e:
            logger.error(f"Error initializing application: {str(e)}")
            messagebox.showerror("خطأ", f"فشل بدء التطبيق: {str(e)}")
            sys.exit(1)

    def setup_styles(self):
        """Configure the application's visual styles"""
        try:
            style = ttk.Style()
            style.theme_use(WINDOW_THEME)
            
            style.configure("TFrame", background=WINDOW_BG_COLOR)
            style.configure("TButton", background=BUTTON_BG_COLOR, foreground=BUTTON_FG_COLOR)
            style.configure("TLabel", background=WINDOW_BG_COLOR, foreground=LABEL_FG_COLOR)
            style.configure("TEntry", background=ENTRY_BG_COLOR, foreground=ENTRY_FG_COLOR)
            style.configure("TCombobox", background=COMBOBOX_BG_COLOR, foreground=COMBOBOX_FG_COLOR)
            
        except Exception as e:
            logger.error(f"Error setting up styles: {str(e)}")

    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="خروج", command=self.show_exit)
        
        # Operations menu
        operations_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عمليات", menu=operations_menu)
        operations_menu.add_command(label="تسجيل قيد", command=self.show_create_entry)
        operations_menu.add_command(label="إصدار فاتورة", command=self.show_issue_invoice)
        operations_menu.add_command(label="عرض العملاء", command=self.show_client_names)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول", command=self.show_about)
        help_menu.add_command(label="تعليمات", command=self.show_help)

    def create_main_interface(self):
        """Create the main interface elements"""
        # Entry frame
        entry_frame = ttk.LabelFrame(self.root, text="تسجيل قيد محاسبي", padding=10)
        entry_frame.pack(padx=10, pady=10, fill="x")
        
        # Date entry
        ttk.Label(entry_frame, text="التاريخ (YYYY-MM-DD):").grid(row=0, column=0, sticky="w")
        self.date_entry = ttk.Entry(entry_frame)
        self.date_entry.grid(row=0, column=1, padx=5, pady=5)
        self.date_entry.insert(0, datetime.now().strftime(DATE_FORMAT))
        
        # Description entry
        ttk.Label(entry_frame, text="الوصف:").grid(row=1, column=0, sticky="w")
        self.desc_entry = ttk.Entry(entry_frame)
        self.desc_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # Amount entry
        ttk.Label(entry_frame, text="المبلغ:").grid(row=2, column=0, sticky="w")
        self.amount_entry = ttk.Entry(entry_frame)
        self.amount_entry.grid(row=2, column=1, padx=5, pady=5)
        
        # Type selection
        ttk.Label(entry_frame, text="النوع:").grid(row=3, column=0, sticky="w")
        self.type_var = tk.StringVar(value=DEFAULT_ENTRY_TYPE)
        type_combo = ttk.Combobox(entry_frame, textvariable=self.type_var, values=ENTRY_TYPES, state="readonly")
        type_combo.grid(row=3, column=1, padx=5, pady=5)
        
        # Register entry button
        register_button = ttk.Button(entry_frame, text="تسجيل القيد", command=self.show_create_entry)
        register_button.grid(row=4, column=0, columnspan=2, pady=10)
        
        # Invoice frame
        invoice_frame = ttk.LabelFrame(self.root, text="إصدار فاتورة", padding=10)
        invoice_frame.pack(padx=10, pady=10, fill="x")
        
        # Client name entry
        ttk.Label(invoice_frame, text="اسم العميل:").grid(row=0, column=0, sticky="w")
        self.client_entry = ttk.Entry(invoice_frame)
        self.client_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # Invoice amount entry
        ttk.Label(invoice_frame, text="المبلغ:").grid(row=1, column=0, sticky="w")
        self.invoice_amount_entry = ttk.Entry(invoice_frame)
        self.invoice_amount_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # Issue invoice button
        invoice_button = ttk.Button(invoice_frame, text="إصدار الفاتورة", command=self.show_issue_invoice)
        invoice_button.grid(row=2, column=0, columnspan=2, pady=10)
        
        # Clients frame
        client_frame = ttk.LabelFrame(self.root, text="العملاء", padding=10)
        client_frame.pack(padx=10, pady=10, fill="x")
        
        # Show clients button
        clients_button = ttk.Button(client_frame, text="عرض أسماء العملاء", command=self.show_client_names)
        clients_button.grid(row=0, column=0, columnspan=2, pady=10)

    def show_create_entry(self):
        """Handle entry creation"""
        try:
            date = self.date_entry.get()
            description = self.desc_entry.get()
            amount = self.amount_entry.get()
            entry_type = self.type_var.get()
            
            try:
                amount = float(amount)
                create_entry(date, description, amount, entry_type)
                messagebox.showinfo("نجاح", MESSAGES['success']['entry_created'])
                self.clear_entry_fields()
            except ValueError:
                messagebox.showerror("خطأ", MESSAGES['error']['invalid_amount'])
            except Exception as e:
                logger.error(f"Error creating entry: {str(e)}")
                messagebox.showerror("خطأ", f"{MESSAGES['error']['db_error']}: {str(e)}")
        except Exception as e:
            logger.error(f"Error in show_create_entry: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def show_issue_invoice(self):
        """Handle invoice creation"""
        try:
            client_name = self.client_entry.get()
            amount = self.invoice_amount_entry.get()
            
            try:
                amount = float(amount)
                issue_invoice(client_name, amount)
                messagebox.showinfo("نجاح", MESSAGES['success']['invoice_issued'])
                self.clear_invoice_fields()
            except ValueError:
                messagebox.showerror("خطأ", MESSAGES['error']['invalid_amount'])
            except Exception as e:
                logger.error(f"Error issuing invoice: {str(e)}")
                messagebox.showerror("خطأ", f"{MESSAGES['error']['db_error']}: {str(e)}")
        except Exception as e:
            logger.error(f"Error in show_issue_invoice: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
        def show_client_names(self):
            """Display client names"""
        try:
            client_names = get_client_names()
            if client_names:
                messagebox.showinfo("أسماء العملاء", "\n".join(client_names))
            else:
                messagebox.showinfo("أسماء العملاء", "لا يوجد عملاء مسجلين")
        except Exception as e:
            logger.error(f"Error in show_client_names: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")


def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("حول", "نظام المحاسبة\nالإصدار 1.0")

def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("حول", "نظام المحاسبة\nالإصدار 1.0")

def show_help(self):
        """Show help dialog"""
        help_text = """تعليمات الاستخدام:
1. لتسجيل قيد محاسبي:
- أدخل التاريخ
- أدخل الوصف
- أدخل المبلغ
- اختر النوع (مدين/دائن)
- اضغط على "تسجيل القيد"

2. لإصدار فاتورة:
- أدخل اسم العميل
- أدخل المبلغ
- اضغط على "إصدار الفاتورة"

3. لعرض أسماء العملاء:
- اضغط على "عرض أسماء العملاء"
"""
        messagebox.showinfo("تعليمات", help_text)

def show_exit(self):
        """Handle application exit"""
        if messagebox.askyesno("خروج", "هل أنت متأكد من الخروج؟"):
            try:
                self.db.close()
                self.root.destroy()
            except Exception as e:
                logger.error(f"Error during exit: {str(e)}")
                self.root.destroy()

def clear_entry_fields(self):
        """Clear entry form fields"""
        self.desc_entry.delete(0, tk.END)
        self.amount_entry.delete(0, tk.END)
        self.date_entry.delete(0, tk.END)
        self.date_entry.insert(0, datetime.now().strftime(DATE_FORMAT))

def clear_invoice_fields(self):
        """Clear invoice form fields"""
        self.client_entry.delete(0, tk.END)
        self.invoice_amount_entry.delete(0, tk.END)

def main():
    """Main function to run the application"""
    try:
        root = tk.Tk()
        app = AccountingApp(root)
        root.mainloop()
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        messagebox.showerror("خطأ", f"حدث خطأ جسيم: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
    