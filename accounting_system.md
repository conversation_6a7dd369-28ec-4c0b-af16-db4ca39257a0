accounting_system\
├── app.py (ملف التطبيق الرئيسي)
├── requirements.txt (ملف المتطلبات البرمجية)
├── models\ (مجلد نماذج البيانات)
│   ├── __init__.py
│   ├── user.py (نموذج المستخدمين)
│   ├── accounting.py (نماذج المحاسبة)
│   ├── customer_supplier.py (نماذج العملاء والموردين)
│   ├── inventory.py (نماذج المخزون)
│   ├── payroll.py (نماذج الرواتب)
│   ├── assets.py (نماذج الأصول الثابتة)
│   ├── reports.py (نماذج التقارير)
│   └── models.py (ملجئ استيراد جميع النماذج)
├── routes\ (مجلد مسارات التطبيق)
│   ├── __init__.py
│   ├── auth_routes.py (مسارات المصادقة)
│   ├── accounting_routes.py (مسارات المحاسبة)
│   ├── customer_routes.py (مسارات العملاء)
│   ├── supplier_routes.py (مسارات الموردين)
│   ├── inventory_routes.py (مسارات المخزون)
│   ├── payroll_routes.py (مسارات الرواتب)
│   ├── asset_routes.py (مسارات الأصول)
│   ├── report_routes.py (مسارات التقارير)
│   └── routes.py (ملجئ استيراد جميع المسارات)
└── templates\ (مجلد القوالب)
    └── layout.html (القالب الرئيسي للتطبيق)
