
// انتظار تحميل المستند بالكامل
$(document).ready(function() {
    // إظهار/إخفاء القوائم المنسدلة
    $('.dropdown-toggle').dropdown();

    // تأكيد الحذف
    $('.confirm-delete').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');

        if (confirm('هل أنت متأكد من أنك تريد حذف هذا العنصر؟')) {
            window.location.href = url;
        }
    });

    // تنسيق التواريخ
    $('.date-input').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        rtl: true
    });

    // تنسيق الأرقام
    $('.number-input').on('input', function() {
        var value = $(this).val();
        value = value.replace(/[^0-9.]/g, '');
        $(this).val(value);
    });

    // تحميل المزيد من البيانات
    $('.load-more').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var container = $(this).data('container');

        $(this).html('<i class="fas fa-spinner fa-spin"></i> جاري التحميل...');

        $.get(url, function(data) {
            $(container).append(data);
            $('.load-more').remove();
        });
    });

    // إظهار رسائل التنبيه تلقائياً
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // منع إرسال النماذج بشكل متكرر
    $('form').on('submit', function() {
        var $form = $(this);
        if ($form.data('submitted')) {
            return false;
        }
        $form.data('submitted', true);
        $form.find('button[type="submit"]').prop('disabled', true);
    });
});
