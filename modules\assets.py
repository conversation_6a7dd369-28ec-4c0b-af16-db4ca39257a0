# assets.py
from datetime import datetime
from enum import Enum
from babel.numbers import format_currency as babel_format_currency
from babel.dates import format_date as babel_format_date
import locale
locale.setlocale(locale.LC_ALL, "ar_EG.UTF-8")

class AssetCategory(Enum):
    EQUIPMENT = "معدات"
    VEHICLE = "مركبة"
    PROPERTY = "عقار"
    OTHER = "أخرى"

class AssetStatus(Enum):
    ACTIVE = "نشط"
    INACTIVE = "غير نشط"
    UNDER_MAINTENANCE = "تحت الصيانة"
    RETIRED = "متقاعد"
    DISPOSED = "منتهية"
    LOST = "مفقود"
    STOLEN = "مسروق"
    DAMAGED = "تالف"
    SOLD = "مباع"
    LEASED = "مؤجر"
    TRANSFERRED = "منقول"
    WRITTEN_OFF = "مشطوب"

class DepreciationMethod(Enum):
    STRAIGHT_LINE = "طريقة القسط الثابت"
    DECLINING_BALANCE = "طريقة الرصيد المتناقص"
    SUM_OF_YEARS = "طريقة مجموع أرقام السنوات"
    UNITS_OF_PRODUCTION = "طريقة وحدات الإنتاج"
    DOUBLE_DECLINING_BALANCE = "طريقة الرصيد المتناقص المزدوج"
    DOUBLE_SUM_OF_YEARS = "طريقة مجموع أرقام السنوات المزدوجة"
    CUSTOM = "طريقة مخصصة"
    NONE = "لا يوجد"
    HYBRID = "طريقة هجينة"
    ACCELERATED = "طريقة متسارعة"

def register_asset(name, value, purchase_date, category=AssetCategory.OTHER, status=AssetStatus.ACTIVE):
    """Registers an asset with the accounting system."""
    try:
        # Validate value
        if value <= 0:
            raise ValueError("القيمة يجب أن تكون موجبة")
            
        # Validate date
        datetime.strptime(purchase_date, "%Y-%m-%d")
        
        print(f"تسجيل أصل: {name} - القيمة: {value} - تاريخ الشراء: {purchase_date} - الفئة: {category.value} - الحالة: {status.value}")
        return True
    except ValueError as e:
        print(f"خطأ في تسجيل الأصل: {str(e)}")
        return False

def calculate_depreciation(value, years, method=DepreciationMethod.STRAIGHT_LINE, current_year=1):
    """Calculates depreciation using different methods."""
    try:
        if value <= 0 or years <= 0:
            raise ValueError("القيمة والسنوات يجب أن تكون موجبة")

        if method == DepreciationMethod.STRAIGHT_LINE:
            depreciation = value / years
        elif method == DepreciationMethod.DECLINING_BALANCE:
            rate = 2 / years
            depreciation = value * rate
        elif method == DepreciationMethod.SUM_OF_YEARS:
            sum_years = years * (years + 1) / 2
            depreciation = (years - current_year + 1) / sum_years * value
        elif method == DepreciationMethod.UNITS_OF_PRODUCTION:
            units_produced = 1000  # Placeholder value
            total_units = 10000  # Placeholder value
            depreciation = (units_produced / total_units) * value
        elif method == DepreciationMethod.DOUBLE_DECLINING_BALANCE:
            rate = 2 / years
            depreciation = value * rate
        elif method == DepreciationMethod.DOUBLE_SUM_OF_YEARS:
            sum_years = years * (years + 1) / 2
            depreciation = 2 * (years - current_year + 1) / sum_years * value
        else:
            depreciation = 0

        return depreciation * current_year
    except ValueError as e:
        print(f"خطأ في حساب الإهلاك: {str(e)}")
        return None

def update_asset_status(asset_id, status=AssetStatus.ACTIVE):
    """Updates the status of an asset."""
    print(f"تحديث حالة الأصل {asset_id}: {status.value}")
    return True

def format_currency(amount, currency="EGP", locale_str="ar_EG"):
    """Formats the amount as currency with the specified currency code and locale."""
    return babel_format_currency(amount, currency, locale=locale_str)

def format_date(date_obj, locale_str="ar_EG"):
    """Formats the date according to the specified locale."""
    return babel_format_date(date_obj, locale=locale_str)

# Example usage
if __name__ == "__main__":
    # 注册多个不同类型的资产
    assets = [
        ("حاسوب محمول", 15000, "2023-01-15", AssetCategory.EQUIPMENT),
        ("سيارة شركة", 200000, "2022-06-01", AssetCategory.VEHICLE),
        ("مبنى مكتب", 1000000, "2020-01-01", AssetCategory.PROPERTY)
    ]
    
    for asset in assets:
        register_asset(*asset)
    
    # 演示不同的折旧计算方法
    value = 15000
    years = 5
    methods = [
        DepreciationMethod.STRAIGHT_LINE,
        DepreciationMethod.DECLINING_BALANCE,
        DepreciationMethod.SUM_OF_YEARS
    ]
    
    print("\nحساب الإهلاك باستخدام طرق مختلفة:")
    for method in methods:
        depreciation = calculate_depreciation(value, years, method)
        print(f"{method.value}: {format_currency(depreciation)}")
    
    # 演示状态更新
    print("\nتحديث حالة الأصول:")
    statuses = [AssetStatus.UNDER_MAINTENANCE, AssetStatus.ACTIVE, AssetStatus.IN_USE]
    for i, status in enumerate(statuses, 1):
        update_asset_status(i, status)
    
    # 演示不同货币和日期格式
    print("\nتنسيق العملات والتواريخ:")
    print(f"الجنيه المصري: {format_currency(15000)}")
    print(f"الدولار الأمريكي: {format_currency(15000, 'USD')}")
    print(f"اليورو: {format_currency(15000, 'EUR')}")
    
    print("\nتنسيق التواريخ:")
    current_date = datetime.now()
    locales = ["ar_EG", "en_US", "fr_FR", "de_DE"]
    for loc in locales:
        print(f"{loc}: {format_date(current_date, loc)}")
def format_currency(amount, currency="EGP"):
    """تنسيق المبلغ كعملة مع رمز العملة المحدد"""
    return babel_format_currency(amount, currency, locale="ar_EG")
    return f"{amount:,.2f} {currency}"  # استخدام التنسيق المناسب 
def format_date(date_obj, locale_str="ar_EG"):
    """تنسيق التاريخ وفقًا للغة المحددة"""
    return babel_format_date(date_obj, locale=locale_str)    
    return date_obj.strftime("%Y-%m-%d")    
    return date_obj.strftime("%Y-%m-%d")    
    return date_obj.strftime("%Y-%m-%d")
    return date_obj.strftime("%Y-%m-%d")    
    return date_obj.strftime("%Y-%m-%d")    
    return date_obj.strftime("%Y-%m-%d")    
    return date_obj.strftime("%Y-%m-%d")    

def format_date(date_obj, locale_str="ar_EG"):
    """Formats the date according to the specified locale."""
    try:
        return babel_format_date(date_obj, locale=locale_str)
    except:
        # Fallback to basic format if babel formatting fails
        return date_obj.strftime("%Y-%m-%d")
    return date_obj.strftime("%Y-%m-%d")    


     