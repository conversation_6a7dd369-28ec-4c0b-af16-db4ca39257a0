
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import Account, JournalEntry, Transaction, BankReconciliation, ReconciliationItem
from decimal import Decimal
from datetime import datetime

accounting_bp = Blueprint('accounting', __name__)

@accounting_bp.route('/dashboard')
@login_required
def dashboard():
    # الحصول على إحصائيات لوحة التحكم
    stats = {
        'total_accounts': Account.query.count(),
        'total_journal_entries': JournalEntry.query.count(),
        'total_transactions': Transaction.query.count(),
        'recent_transactions': Transaction.query.order_by(Transaction.created_at.desc()).limit(5).all(),
        'recent_journal_entries': JournalEntry.query.order_by(JournalEntry.created_at.desc()).limit(5).all()
    }

    return render_template('accounting/dashboard.html', stats=stats)

@accounting_bp.route('/accounts')
@login_required
def accounts():
    # الحصول على جميع الحسابات
    accounts = Account.query.all()
    return render_template('accounting/accounts.html', accounts=accounts)

@accounting_bp.route('/accounts/new', methods=['GET', 'POST'])
@login_required
def new_account():
    if request.method == 'POST':
        account_number = request.form.get('account_number')
        name = request.form.get('name')
        account_type = request.form.get('account_type')
        parent_id = request.form.get('parent_id') or None

        # التحقق من عدم تكرار رقم الحساب
        existing_account = Account.query.filter_by(account_number=account_number).first()
        if existing_account:
            flash('رقم الحساب موجود بالفعل', 'danger')
            return render_template('accounting/new_account.html')

        # إنشاء الحساب الجديد
        new_account = Account(
            account_number=account_number,
            name=name,
            account_type=account_type,
            parent_id=parent_id
        )

        db.session.add(new_account)
        db.session.commit()

        flash('تم إنشاء الحساب بنجاح', 'success')
        return redirect(url_for('accounting.accounts'))

    # الحصول على جميع الحسابات لاستخدامها كآباء محتملين
    accounts = Account.query.all()
    return render_template('accounting/new_account.html', accounts=accounts)

@accounting_bp.route('/journal-entries')
@login_required
def journal_entries():
    # الحصول على جميع القيود اليومية
    entries = JournalEntry.query.order_by(JournalEntry.date.desc()).all()
    return render_template('accounting/journal_entries.html', entries=entries)

@accounting_bp.route('/journal-entries/new', methods=['GET', 'POST'])
@login_required
def new_journal_entry():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        description = request.form.get('description')

        # إنشاء المعاملة
        transaction = Transaction(
            date=date,
            description=description,
            amount=Decimal('0.00'),  # سيتم تحديثه لاحقًا
            status='draft',
            created_by_id=current_user.id
        )
        db.session.add(transaction)
        db.session.flush()  # للحصول على ID المعاملة

        # جمع القيود
        total_debit = Decimal('0.00')
        total_credit = Decimal('0.00')

        for key, value in request.form.items():
            if key.startswith('account_'):
                account_id = int(key.split('_')[1])
                amount = Decimal(value)

                if amount > 0:
                    is_debit = request.form.get(f'type_{account_id}') == 'debit'

                    if is_debit:
                        total_debit += amount
                    else:
                        total_credit += amount

                    # إنشاء القيد اليومي
                    journal_entry = JournalEntry(
                        transaction_id=transaction.id,
                        account_id=account_id,
                        date=date,
                        description=description,
                        is_debit=is_debit,
                        amount=amount,
                        created_by_id=current_user.id
                    )
                    db.session.add(journal_entry)

        # التحقق من توازن المعاملة
        if total_debit != total_credit:
            flash('المجموع المدي يجب أن يساوي المجموع الدائن', 'danger')
            db.session.rollback()
            return render_template('accounting/new_journal_entry.html')

        # تحديث مبلغ المعاملة
        transaction.amount = total_debit  # أو total_credit، فهما متساويين
        transaction.status = 'posted'
        db.session.commit()

        flash('تم تسجيل القيود اليومية بنجاح', 'success')
        return redirect(url_for('accounting.journal_entries'))

    # الحصول على جميع الحسابات لعرضها في النموذج
    accounts = Account.query.all()
    return render_template('accounting/new_journal_entry.html', accounts=accounts)

@accounting_bp.route('/transactions')
@login_required
def transactions():
    # الحصول على جميع المعاملات
    transactions = Transaction.query.order_by(Transaction.date.desc()).all()
    return render_template('accounting/transactions.html', transactions=transactions)

@accounting_bp.route('/transactions/new', methods=['GET', 'POST'])
@login_required
def new_transaction():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        description = request.form.get('description')
        amount = Decimal(request.form.get('amount'))

        # إنشاء المعاملة
        transaction = Transaction(
            date=date,
            description=description,
            amount=amount,
            status='draft',
            created_by_id=current_user.id
        )
        db.session.add(transaction)
        db.session.flush()  # للحصول على ID المعاملة

        # إنشاء القيود اليومية المقابلة
        # في نظام محاسبي حقيقي، سيتم تحديد الحسابات تلقائيًا بناءً على نوع المعاملة
        # هنا سنقوم بإنشاء قيود افتراضية للتوضيح

        # القيد الأول: مدين (Debit)
        debit_account_id = request.form.get('debit_account_id')
        if debit_account_id:
            debit_entry = JournalEntry(
                transaction_id=transaction.id,
                account_id=debit_account_id,
                date=date,
                description=description,
                is_debit=True,
                amount=amount,
                created_by_id=current_user.id
            )
            db.session.add(debit_entry)

        # القيد الثاني: دائن (Credit)
        credit_account_id = request.form.get('credit_account_id')
        if credit_account_id:
            credit_entry = JournalEntry(
                transaction_id=transaction.id,
                account_id=credit_account_id,
                date=date,
                description=description,
                is_debit=False,
                amount=amount,
                created_by_id=current_user.id
            )
            db.session.add(credit_entry)

        # تحديث حالة المعاملة
        transaction.status = 'posted'
        db.session.commit()

        flash('تم تسجيل المعاملة بنجاح', 'success')
        return redirect(url_for('accounting.transactions'))

    # الحصول على جميع الحسابات لعرضها في النموذج
    accounts = Account.query.all()
    return render_template('accounting/new_transaction.html', accounts=accounts)

@accounting_bp.route('/bank-reconciliations')
@login_required
def bank_reconciliations():
    # الحصول على جميع التسويات البنكية
    reconciliations = BankReconciliation.query.order_by(BankReconciliation.bank_statement_date.desc()).all()
    return render_template('accounting/bank_reconciliations.html', reconciliations=reconciliations)

@accounting_bp.route('/bank-reconciliations/new', methods=['GET', 'POST'])
@login_required
def new_bank_reconciliation():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        account_id = request.form.get('account_id')
        bank_statement_date = datetime.strptime(request.form.get('bank_statement_date'), '%Y-%m-%d').date()
        bank_statement_balance = Decimal(request.form.get('bank_statement_balance'))
        book_balance = Decimal(request.form.get('book_balance'))

        # حساب الفارق
        difference = bank_statement_balance - book_balance

        # إنشاء تسوية البنك الجديدة
        reconciliation = BankReconciliation(
            account_id=account_id,
            bank_statement_date=bank_statement_date,
            bank_statement_balance=bank_statement_balance,
            book_balance=book_balance,
            difference=difference,
            created_by_id=current_user.id
        )
        db.session.add(reconciliation)
        db.session.commit()

        flash('تم إنشاء التسوية البنكية بنجاح', 'success')
        return redirect(url_for('accounting.bank_reconciliations'))

    # الحصول على جميع الحسابات البنكية لعرضها في النموذج
    accounts = Account.query.filter_by(account_type='asset').all()
    return render_template('accounting/new_bank_reconciliation.html', accounts=accounts)
