
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import Employee, Department, Position, AllowanceType, DeductionType, Allowance, Deduction, EmployeeAllowance, EmployeeDeduction, LeaveType, Leave, PayrollPeriod, PayrollRecord, TaxRule
from decimal import Decimal
from datetime import datetime

payroll_bp = Blueprint('payroll', __name__)

@payroll_bp.route('/')
@login_required
def payroll_dashboard():
    # الحصول على إحصائيات لوحة التحكم الرواتب
    stats = {
        'total_employees': Employee.query.count(),
        'active_employees': Employee.query.filter_by(is_active=True).count(),
        'pending_leaves': Leave.query.filter_by(status='pending').count(),
        'current_period': PayrollPeriod.query.filter_by(status='active').first()
    }

    return render_template('payroll/dashboard.html', stats=stats)

@payroll_bp.route('/employees')
@login_required
def employees():
    # الحصول على جميع الموظفين
    employees = Employee.query.all()
    return render_template('payroll/employees.html', employees=employees)

@payroll_bp.route('/employees/new', methods=['GET', 'POST'])
@login_required
def new_employee():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        employee_number = request.form.get('employee_number')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        hire_date = datetime.strptime(request.form.get('hire_date'), '%Y-%m-%d').date()
        salary = Decimal(request.form.get('salary', 0))
        department_id = request.form.get('department_id')
        position_id = request.form.get('position_id')
        bank_account = request.form.get('bank_account')
        bank_name = request.form.get('bank_name')
        tax_id = request.form.get('tax_id')
        social_security_number = request.form.get('social_security_number')

        # التحقق من عدم تكرار رقم الموظف
        existing_employee = Employee.query.filter_by(employee_number=employee_number).first()
        if existing_employee:
            flash('رقم الموظف موجود بالفعل', 'danger')
            return render_template('payroll/new_employee.html')

        # إنشاء الموظف الجديد
        new_employee = Employee(
            employee_number=employee_number,
            first_name=first_name,
            last_name=last_name,
            email=email,
            phone=phone,
            address=address,
            hire_date=hire_date,
            salary=salary,
            department_id=department_id,
            position_id=position_id,
            bank_account=bank_account,
            bank_name=bank_name,
            tax_id=tax_id,
            social_security_number=social_security_number
        )

        db.session.add(new_employee)
        db.session.commit()

        flash('تم إنشاء الموظف بنجاح', 'success')
        return redirect(url_for('payroll.employees'))

    # الحصول على جميع الأقسام والوظائف لعرضها في النموذج
    departments = Department.query.all()
    positions = Position.query.all()
    return render_template('payroll/new_employee.html', departments=departments, positions=positions)

@payroll_bp.route('/employees/<employee_id>')
@login_required
def employee_detail(employee_id):
    # الحصول على تفاصيل الموظف
    employee = Employee.query.get_or_404(employee_id)

    # الحصول على الإعانات والخصومات الخاصة بالموظف
    allowances = EmployeeAllowance.query.filter_by(employee_id=employee_id, is_active=True).all()
    deductions = EmployeeDeduction.query.filter_by(employee_id=employee_id, is_active=True).all()

    # الحصول على الإجازات الخاصة بالموظف
    leaves = Leave.query.filter_by(employee_id=employee_id).order_by(Leave.start_date.desc()).all()

    # الحصول على سجلات الرواتب الخاصة بالموظف
    payroll_records = PayrollRecord.query.filter_by(employee_id=employee_id).order_by(PayrollRecord.created_at.desc()).all()

    return render_template('payroll/employee_detail.html', employee=employee, allowances=allowances, deductions=deductions, leaves=leaves, payroll_records=payroll_records)

@payroll_bp.route('/employees/<employee_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_employee(employee_id):
    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    if request.method == 'POST':
        # تحديث بيانات الموظف
        employee.employee_number = request.form.get('employee_number')
        employee.first_name = request.form.get('first_name')
        employee.last_name = request.form.get('last_name')
        employee.email = request.form.get('email')
        employee.phone = request.form.get('phone')
        employee.address = request.form.get('address')
        employee.hire_date = datetime.strptime(request.form.get('hire_date'), '%Y-%m-%d').date()
        employee.salary = Decimal(request.form.get('salary', 0))
        employee.department_id = request.form.get('department_id')
        employee.position_id = request.form.get('position_id')
        employee.bank_account = request.form.get('bank_account')
        employee.bank_name = request.form.get('bank_name')
        employee.tax_id = request.form.get('tax_id')
        employee.social_security_number = request.form.get('social_security_number')

        db.session.commit()

        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('payroll.employee_detail', employee_id=employee_id))

    # الحصول على جميع الأقسام والوظائف لعرضها في النموذج
    departments = Department.query.all()
    positions = Position.query.all()
    return render_template('payroll/edit_employee.html', employee=employee, departments=departments, positions=positions)

@payroll_bp.route('/employees/<employee_id>/delete', methods=['POST'])
@login_required
def delete_employee(employee_id):
    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    # التحقق من عدم وجود سجلات رواتب للموظف
    if employee.payroll_records.count() > 0:
        flash('لا يمكن حذف الموظف لأن لديه سجلات رواتب مسجلة', 'danger')
        return redirect(url_for('payroll.employee_detail', employee_id=employee_id))

    db.session.delete(employee)
    db.session.commit()

    flash('تم حذف الموظف بنجاح', 'success')
    return redirect(url_for('payroll.employees'))

@payroll_bp.route('/departments')
@login_required
def departments():
    # الحصول على جميع الأقسام
    departments = Department.query.all()
    return render_template('payroll/departments.html', departments=departments)

@payroll_bp.route('/departments/new', methods=['GET', 'POST'])
@login_required
def new_department():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        manager_id = request.form.get('manager_id') or None

        # إنشاء القسم الجديد
        new_department = Department(
            name=name,
            description=description,
            manager_id=manager_id
        )

        db.session.add(new_department)
        db.session.commit()

        flash('تم إنشاء القسم بنجاح', 'success')
        return redirect(url_for('payroll.departments'))

    # الحصول على جميع الموظفين لاستخدامهم كمديرين محتملين
    employees = Employee.query.filter_by(is_active=True).all()
    return render_template('payroll/new_department.html', employees=employees)

@payroll_bp.route('/positions')
@login_required
def positions():
    # الحصول على جميع الوظائف
    positions = Position.query.all()
    return render_template('payroll/positions.html', positions=positions)

@payroll_bp.route('/positions/new', methods=['GET', 'POST'])
@login_required
def new_position():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        grade = request.form.get('grade')

        # إنشاء الوظيفة الجديدة
        new_position = Position(
            name=name,
            description=description,
            grade=grade
        )

        db.session.add(new_position)
        db.session.commit()

        flash('تم إنشاء الوظيفة بنجاح', 'success')
        return redirect(url_for('payroll.positions'))

    return render_template('payroll/new_position.html')

@payroll_bp.route('/allowances')
@login_required
def allowances():
    # الحصول على جميع أنواع الإعانات
    allowance_types = AllowanceType.query.all()
    return render_template('payroll/allowances.html', allowance_types=allowance_types)

@payroll_bp.route('/allowances/new', methods=['GET', 'POST'])
@login_required
def new_allowance_type():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        is_percentage = request.form.get('is_percentage') == '1'
        is_taxable = request.form.get('is_taxable') == '1'

        # إنشاء نوع الإعانة الجديد
        new_allowance_type = AllowanceType(
            name=name,
            description=description,
            is_percentage=is_percentage,
            is_taxable=is_taxable
        )

        db.session.add(new_allowance_type)
        db.session.commit()

        flash('تم إنشاء نوع الإعانة بنجاح', 'success')
        return redirect(url_for('payroll.allowances'))

    return render_template('payroll/new_allowance_type.html')

@payroll_bp.route('/deductions')
@login_required
def deductions():
    # الحصول على جميع أنواع الخصومات
    deduction_types = DeductionType.query.all()
    return render_template('payroll/deductions.html', deduction_types=deduction_types)

@payroll_bp.route('/deductions/new', methods=['GET', 'POST'])
@login_required
def new_deduction_type():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        is_percentage = request.form.get('is_percentage') == '1'

        # إنشاء نوع الخصم الجديد
        new_deduction_type = DeductionType(
            name=name,
            description=description,
            is_percentage=is_percentage
        )

        db.session.add(new_deduction_type)
        db.session.commit()

        flash('تم إنشاء نوع الخصم بنجاح', 'success')
        return redirect(url_for('payroll.deductions'))

    return render_template('payroll/new_deduction_type.html')

@payroll_bp.route('/leaves')
@login_required
def leaves():
    # الحصول على جميع الإجازات
    leaves = Leave.query.order_by(Leave.created_at.desc()).all()
    return render_template('payroll/leaves.html', leaves=leaves)

@payroll_bp.route('/leaves/new', methods=['GET', 'POST'])
@login_required
def new_leave():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        employee_id = request.form.get('employee_id')
        leave_type_id = request.form.get('leave_type_id')
        start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
        end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
        reason = request.form.get('reason')

        # حساب عدد أيام الإجازة
        days_count = (end_date - start_date).days + 1

        # إنشاء الإجازة الجديدة
        new_leave = Leave(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            start_date=start_date,
            end_date=end_date,
            days_count=days_count,
            reason=reason
        )

        db.session.add(new_leave)
        db.session.commit()

        flash('تم إنشاء طلب الإجازة بنجاح', 'success')
        return redirect(url_for('payroll.leaves'))

    # الحصول على جميع الموظفين وأنواع الإجازات لعرضها في النموذج
    employees = Employee.query.filter_by(is_active=True).all()
    leave_types = LeaveType.query.all()
    return render_template('payroll/new_leave.html', employees=employees, leave_types=leave_types)

@payroll_bp.route('/leaves/<leave_id>/approve', methods=['POST'])
@login_required
def approve_leave(leave_id):
    # الحصول على الإجازة
    leave = Leave.query.get_or_404(leave_id)

    # تحديث حالة الإجازة
    leave.status = 'approved'
    leave.approved_by_id = current_user.id
    leave.approved_at = datetime.utcnow()

    db.session.commit()

    flash('تم الموافقة على طلب الإجازة', 'success')
    return redirect(url_for('payroll.leaves'))

@payroll_bp.route('/leaves/<leave_id>/reject', methods=['POST'])
@login_required
def reject_leave(leave_id):
    # الحصول على الإجازة
    leave = Leave.query.get_or_404(leave_id)

    # تحديث حالة الإجازة
    leave.status = 'rejected'
    leave.approved_by_id = current_user.id
    leave.approved_at = datetime.utcnow()

    db.session.commit()

    flash('تم رفض طلب الإجازة', 'success')
    return redirect(url_for('payroll.leaves'))

@payroll_bp.route('/payroll-periods')
@login_required
def payroll_periods():
    # الحصول على جميع فترات الرواتب
    periods = PayrollPeriod.query.order_by(PayrollPeriod.created_at.desc()).all()
    return render_template('payroll/payroll_periods.html', periods=periods)

@payroll_bp.route('/payroll-periods/new', methods=['GET', 'POST'])
@login_required
def new_payroll_period():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
        end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
        payment_date = datetime.strptime(request.form.get('payment_date'), '%Y-%m-%d').date()
        notes = request.form.get('notes')

        # إنشاء فترة الرواتب الجديدة
        new_period = PayrollPeriod(
            name=name,
            start_date=start_date,
            end_date=end_date,
            payment_date=payment_date,
            notes=notes
        )

        db.session.add(new_period)
        db.session.commit()

        flash('تم إنشاء فترة الرواتب بنجاح', 'success')
        return redirect(url_for('payroll.payroll_periods'))

    return render_template('payroll/new_payroll_period.html')

@payroll_bp.route('/payroll-records')
@login_required
def payroll_records():
    # الحصول على جميع سجلات الرواتب
    records = PayrollRecord.query.order_by(PayrollRecord.created_at.desc()).all()
    return render_template('payroll/payroll_records.html', records=records)

@payroll_bp.route('/payroll-records/<period_id>')
@login_required
def payroll_period_records(period_id):
    # الحصول على فترة الرواتب
    period = PayrollPeriod.query.get_or_404(period_id)

    # الحصول على جميع سجلات الرواتب الخاصة بهذه الفترة
    records = PayrollRecord.query.filter_by(payroll_period_id=period_id).all()

    return render_template('payroll/payroll_period_records.html', period=period, records=records)

@payroll_bp.route('/payroll-records/process/<period_id>', methods=['GET', 'POST'])
@login_required
def process_payroll_period(period_id):
    # الحصول على فترة الرواتب
    period = PayrollPeriod.query.get_or_404(period_id)

    if request.method == 'POST':
        # تحديث حالة فترة الرواتب
        period.status = 'processing'
        db.session.commit()

        # الحصول على جميع الموظفين النشطين
        employees = Employee.query.filter_by(is_active=True).all()

        # معالجة كل موظف على حدة
        for employee in employees:
            # حساب الرواتب الأساسية والمتغيرة
            basic_salary = employee.salary / 12  # تحويل الراتب السنوي إلى شهري

            # حساب الإعانات
            allowances = Decimal('0.00')
            for allowance in employee.allowances.filter_by(is_active=True).all():
                if allowance.allowance_type.is_percentage:
                    allowances += basic_salary * (allowance.amount / 100)
                else:
                    allowances += allowance.amount

            # حساب الخصومات
            deductions = Decimal('0.00')
            for deduction in employee.deductions.filter_by(is_active=True).all():
                if deduction.deduction_type.is_percentage:
                    deductions += basic_salary * (deduction.amount / 100)
                else:
                    deductions += deduction.amount

            # حساب الضرائب
            # سيتم تطبيق منطق حساب الضرائب المعقد هنا
            tax_amount = Decimal('0.00')

            # حساب صافي الراتب
            net_pay = basic_salary + allowances - deductions - tax_amount

            # إنشاء سجل الرواتب للموظف
            payroll_record = PayrollRecord(
                employee_id=employee.id,
                payroll_period_id=period.id,
                basic_salary=basic_salary,
                allowances=allowances,
                deductions=deductions,
                tax_amount=tax_amount,
                net_pay=net_pay,
                payment_method='bank'
            )

            db.session.add(payroll_record)

        # تحديث حالة فترة الرواتب
        period.status = 'completed'
        db.session.commit()

        flash('تم معالجة فترة الرواتب بنجاح', 'success')
        return redirect(url_for('payroll.payroll_period_records', period_id=period_id))

    return render_template('payroll/process_payroll_period.html', period=period)
