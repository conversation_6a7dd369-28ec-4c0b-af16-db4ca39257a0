
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import AssetCategory, Asset, Vendor, Depreciation, AssetDisposal, AssetTransfer
from decimal import Decimal
from datetime import datetime

asset_bp = Blueprint('assets', __name__)

@asset_bp.route('/')
@login_required
def assets_dashboard():
    # الحصول على إحصائيات لوحة التحكم الأصول
    stats = {
        'total_assets': Asset.query.count(),
        'active_assets': Asset.query.filter_by(status='active').count(),
        'disposed_assets': Asset.query.filter_by(status='disposed').count(),
        'total_categories': AssetCategory.query.count()
    }

    return render_template('assets/dashboard.html', stats=stats)

@asset_bp.route('/assets')
@login_required
def assets():
    # الحصول على جميع الأصول
    assets = Asset.query.all()
    return render_template('assets/assets.html', assets=assets)

@asset_bp.route('/assets/new', methods=['GET', 'POST'])
@login_required
def new_asset():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        asset_number = request.form.get('asset_number')
        name = request.form.get('name')
        description = request.form.get('description')
        category_id = request.form.get('category_id')
        purchase_date = datetime.strptime(request.form.get('purchase_date'), '%Y-%m-%d').date()
        purchase_price = Decimal(request.form.get('purchase_price', 0))
        useful_life = int(request.form.get('useful_life', 0))
        residual_value = Decimal(request.form.get('residual_value', 0))
        depreciation_method = request.form.get('depreciation_method', 'straight_line')
        location = request.form.get('location')
        responsible_person = request.form.get('responsible_person')
        vendor_id = request.form.get('vendor_id')
        warranty_expiry = datetime.strptime(request.form.get('warranty_expiry'), '%Y-%m-%d').date() if request.form.get('warranty_expiry') else None

        # التحقق من عدم تكرار رقم الأصل
        existing_asset = Asset.query.filter_by(asset_number=asset_number).first()
        if existing_asset:
            flash('رقم الأصل موجود بالفعل', 'danger')
            return render_template('assets/new_asset.html')

        # إنشاء الأصل الجديد
        new_asset = Asset(
            asset_number=asset_number,
            name=name,
            description=description,
            category_id=category_id,
            purchase_date=purchase_date,
            purchase_price=purchase_price,
            useful_life=useful_life,
            residual_value=residual_value,
            depreciation_method=depreciation_method,
            current_value=purchase_price,
            accumulated_depreciation=Decimal('0.00'),
            status='active',
            location=location,
            responsible_person=responsible_person,
            vendor_id=vendor_id,
            warranty_expiry=warranty_expiry
        )

        db.session.add(new_asset)
        db.session.commit()

        flash('تم إنشاء الأصل بنجاح', 'success')
        return redirect(url_for('assets.assets'))

    # الحصول على جميع الفئات والموردين لعرضها في النموذج
    categories = AssetCategory.query.all()
    vendors = Vendor.query.all()
    return render_template('assets/new_asset.html', categories=categories, vendors=vendors)

@asset_bp.route('/assets/<asset_id>')
@login_required
def asset_detail(asset_id):
    # الحصول على تفاصيل الأصل
    asset = Asset.query.get_or_404(asset_id)

    # الحصول على إهلاكات الأصل
    depreciations = Depreciation.query.filter_by(asset_id=asset_id).order_by(Depreciation.fiscal_year.desc(), Depreciation.fiscal_period.desc()).all()

    # الحصول على عمليات التخلص من الأصل
    disposals = AssetDisposal.query.filter_by(asset_id=asset_id).all()

    # الحصول على عمليات نقل الأصل
    transfers = AssetTransfer.query.filter_by(asset_id=asset_id).all()

    return render_template('assets/asset_detail.html', asset=asset, depreciations=depreciations, disposals=disposals, transfers=transfers)

@asset_bp.route('/assets/<asset_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_asset(asset_id):
    # الحصول على الأصل
    asset = Asset.query.get_or_404(asset_id)

    if request.method == 'POST':
        # تحديث بيانات الأصل
        asset.asset_number = request.form.get('asset_number')
        asset.name = request.form.get('name')
        asset.description = request.form.get('description')
        asset.category_id = request.form.get('category_id')
        asset.purchase_date = datetime.strptime(request.form.get('purchase_date'), '%Y-%m-%d').date()
        asset.purchase_price = Decimal(request.form.get('purchase_price', 0))
        asset.useful_life = int(request.form.get('useful_life', 0))
        asset.residual_value = Decimal(request.form.get('residual_value', 0))
        asset.depreciation_method = request.form.get('depreciation_method', 'straight_line')
        asset.location = request.form.get('location')
        asset.responsible_person = request.form.get('responsible_person')
        asset.vendor_id = request.form.get('vendor_id')
        asset.warranty_expiry = datetime.strptime(request.form.get('warranty_expiry'), '%Y-%m-%d').date() if request.form.get('warranty_expiry') else None

        db.session.commit()

        flash('تم تحديث بيانات الأصل بنجاح', 'success')
        return redirect(url_for('assets.asset_detail', asset_id=asset_id))

    # الحصول على جميع الفئات والموردين لعرضها في النموذج
    categories = AssetCategory.query.all()
    vendors = Vendor.query.all()
    return render_template('assets/edit_asset.html', asset=asset, categories=categories, vendors=vendors)

@asset_bp.route('/assets/<asset_id>/delete', methods=['POST'])
@login_required
def delete_asset(asset_id):
    # الحصول على الأصل
    asset = Asset.query.get_or_404(asset_id)

    # التحقق من عدم وجود إهلاكات أو عمليات تخلص أو نقل للأصل
    if asset.depreciations.count() > 0 or asset.disposals.count() > 0 or asset.transfers.count() > 0:
        flash('لا يمكن حذف الأصل لأن لديه إهلاكات أو عمليات تخلص أو نقل مسجلة', 'danger')
        return redirect(url_for('assets.asset_detail', asset_id=asset_id))

    db.session.delete(asset)
    db.session.commit()

    flash('تم حذف الأصل بنجاح', 'success')
    return redirect(url_for('assets.assets'))

@asset_bp.route('/categories')
@login_required
def categories():
    # الحصول على جميع فئات الأصول
    categories = AssetCategory.query.all()
    return render_template('assets/categories.html', categories=categories)

@asset_bp.route('/categories/new', methods=['GET', 'POST'])
@login_required
def new_category():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        description = request.form.get('description')
        depreciation_method = request.form.get('depreciation_method', 'straight_line')
        useful_life = int(request.form.get('useful_life', 0))
        residual_value_percent = Decimal(request.form.get('residual_value_percent', 0))

        # إنشاء الفئة الجديدة
        new_category = AssetCategory(
            name=name,
            description=description,
            depreciation_method=depreciation_method,
            useful_life=useful_life,
            residual_value_percent=residual_value_percent
        )

        db.session.add(new_category)
        db.session.commit()

        flash('تم إنشاء الفئة بنجاح', 'success')
        return redirect(url_for('assets.categories'))

    return render_template('assets/new_category.html')

@asset_bp.route('/vendors')
@login_required
def vendors():
    # الحصول على جميع الموردين
    vendors = Vendor.query.all()
    return render_template('assets/vendors.html', vendors=vendors)

@asset_bp.route('/vendors/new', methods=['GET', 'POST'])
@login_required
def new_vendor():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        tax_id = request.form.get('tax_id')

        # إنشاء المورد الجديد
        new_vendor = Vendor(
            name=name,
            contact_person=contact_person,
            email=email,
            phone=phone,
            address=address,
            tax_id=tax_id
        )

        db.session.add(new_vendor)
        db.session.commit()

        flash('تم إنشاء المورد بنجاح', 'success')
        return redirect(url_for('assets.vendors'))

    return render_template('assets/new_vendor.html')

@asset_bp.route('/depreciations')
@login_required
def depreciations():
    # الحصول على جميع عمليات الإهلاك
    depreciations = Depreciation.query.order_by(Depreciation.fiscal_year.desc(), Depreciation.fiscal_period.desc()).all()
    return render_template('assets/depreciations.html', depreciations=depreciations)

@asset_bp.route('/depreciations/new', methods=['GET', 'POST'])
@login_required
def new_depreciation():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        asset_id = request.form.get('asset_id')
        fiscal_year = int(request.form.get('fiscal_year'))
        fiscal_period = int(request.form.get('fiscal_period'))

        # الحصول على الأصل
        asset = Asset.query.get(asset_id)

        # حساب مبلغ الإهلاك
        if asset.depreciation_method == 'straight_line':
            # طريقة الإهلاك الخطي
            depreciable_amount = asset.purchase_price - asset.residual_value
            annual_depreciation = depreciable_amount / asset.useful_life
            depreciation_amount = annual_depreciation / 12  # شهر واحد
        elif asset.depreciation_method == 'reducing_balance':
            # طريقة الإهلاك المتناقص
            book_value = asset.current_value - asset.accumulated_depreciation
            depreciation_rate = (1 / asset.useful_life) * 2  # نسبة مزدوجة
            depreciation_amount = book_value * depreciation_rate
        else:
            # طريقة وحدات الإنتاج (غير مطبقة هنا)
            depreciation_amount = Decimal('0.00')

        # التأكد من أن مبلغ الإهلاك لا يتجاوز القيمة المتبقية
        remaining_value = asset.current_value - asset.accumulated_depreciation
        if depreciation_amount > remaining_value:
            depreciation_amount = remaining_value

        # حساب القيمة المجمعة للإهلاك
        accumulated_depreciation = asset.accumulated_depreciation + depreciation_amount

        # حساب القيمة الدفترية
        book_value = asset.current_value - accumulated_depreciation

        # إنشاء عملية الإهلاك
        depreciation = Depreciation(
            asset_id=asset_id,
            fiscal_year=fiscal_year,
            fiscal_period=fiscal_period,
            depreciation_amount=depreciation_amount,
            accumulated_depreciation=accumulated_depreciation,
            book_value=book_value
        )
        db.session.add(depreciation)
        db.session.flush()  # للحصول على ID عملية الإهلاك

        # تحديث الأصل
        asset.accumulated_depreciation = accumulated_depreciation
        asset.current_value = book_value

        db.session.commit()

        flash('تم تسجيل الإهلاك بنجاح', 'success')
        return redirect(url_for('assets.depreciations'))

    # الحصول على جميع الأصول لعرضها في النموذج
    assets = Asset.query.filter_by(status='active').all()
    return render_template('assets/new_depreciation.html', assets=assets)

@asset_bp.route('/disposals')
@login_required
def disposals():
    # الحصول على جميع عمليات التخلص
    disposals = AssetDisposal.query.order_by(AssetDisposal.disposal_date.desc()).all()
    return render_template('assets/disposals.html', disposals=disposals)

@asset_bp.route('/disposals/new', methods=['GET', 'POST'])
@login_required
def new_disposal():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        asset_id = request.form.get('asset_id')
        disposal_date = datetime.strptime(request.form.get('disposal_date'), '%Y-%m-%d').date()
        disposal_method = request.form.get('disposal_method')
        disposal_value = Decimal(request.form.get('disposal_value', 0))
        buyer_name = request.form.get('buyer_name')
        reason = request.form.get('reason')
        notes = request.form.get('notes')

        # إنشاء عملية التخلص
        disposal = AssetDisposal(
            asset_id=asset_id,
            disposal_date=disposal_date,
            disposal_method=disposal_method,
            disposal_value=disposal_value,
            buyer_name=buyer_name,
            reason=reason,
            notes=notes
        )
        db.session.add(disposal)
        db.session.flush()  # للحصول على ID عملية التخلص

        # تحديث حالة الأصل
        asset = Asset.query.get(asset_id)
        asset.status = 'disposed'

        db.session.commit()

        flash('تم تسجيل عملية التخلص بنجاح', 'success')
        return redirect(url_for('assets.disposals'))

    # الحصول على جميع الأصول النشطة لعرضها في النموذج
    assets = Asset.query.filter_by(status='active').all()
    return render_template('assets/new_disposal.html', assets=assets)

@asset_bp.route('/transfers')
@login_required
def transfers():
    # الحصول على جميع عمليات نقل الأصول
    transfers = AssetTransfer.query.order_by(AssetTransfer.transfer_date.desc()).all()
    return render_template('assets/transfers.html', transfers=transfers)

@asset_bp.route('/transfers/new', methods=['GET', 'POST'])
@login_required
def new_transfer():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        asset_id = request.form.get('asset_id')
        from_location = request.form.get('from_location')
        to_location = request.form.get('to_location')
        transfer_date = datetime.strptime(request.form.get('transfer_date'), '%Y-%m-%d').date()
        reason = request.form.get('reason')
        notes = request.form.get('notes')

        # إنشاء عملية النقل
        transfer = AssetTransfer(
            asset_id=asset_id,
            from_location=from_location,
            to_location=to_location,
            transfer_date=transfer_date,
            reason=reason,
            notes=notes
        )
        db.session.add(transfer)
        db.session.commit()

        flash('تم تسجيل عملية النقل بنجاح', 'success')
        return redirect(url_for('assets.transfers'))

    # الحصول على جميع الأصول النشطة لعرضها في النموذج
    assets = Asset.query.filter_by(status='active').all()
    return render_template('assets/new_transfer.html', assets=assets)
