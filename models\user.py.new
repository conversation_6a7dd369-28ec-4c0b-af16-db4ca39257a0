from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid
import random
import string

# تأجيل استيراد db و login_manager حتى يتم استيراد النماذج في app.py
db = None
login_manager = None

class User(UserMixin):
    pass

def init_user_model(database, login_manager_instance):
    """تهيئة نموذج المستخدم مع كائن قاعدة البيانات
    """
    global db, login_manager
    db = database
    login_manager = login_manager_instance

    # إضافة حقول النموذج بعد تهيئة db
    User.id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    User.username = db.Column(db.String(80), unique=True, nullable=False)
    User.email = db.Column(db.String(120), unique=True, nullable=False)
    User.password_hash = db.Column(db.String(255), nullable=False)
    User.first_name = db.Column(db.String(80), nullable=False)
    User.last_name = db.Column(db.String(80), nullable=False)
    User.is_active = db.Column(db.Boolean, default=True)
    User.is_admin = db.Column(db.Boolean, default=False)
    User.created_at = db.Column(db.DateTime, default=datetime.utcnow)
    User.last_login = db.Column(db.DateTime)

    # العلاقات
    User.created_transactions = db.relationship('Transaction', backref='creator', lazy='dynamic')
    User.created_journal_entries = db.relationship('JournalEntry', backref='creator', lazy='dynamic')

def set_password(self, password):
    self.password_hash = generate_password_hash(password)

def check_password(self, password):
    return check_password_hash(self.password_hash, password)

def __repr__(self):
    return f'<User {self.username}>'

@staticmethod
def generate_random_password(length=12):
    """توليد كلمة مرور عشوائية

    Args:
        length (int): طول كلمة المرور (افتراضي: 12)

    Returns:
        str: كلمة مرور عشوائية
    """
    characters = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(random.choice(characters) for i in range(length))
    return password

def user_loader(user_id):
    return User.query.get(user_id)
