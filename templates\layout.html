
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المحاسبة{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-calculator"></i> نظام المحاسبة
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.is_authenticated %}
                    <!-- المحاسبة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="accountingDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-book"></i> المحاسبة
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('accounting.dashboard') }}">لوحة التحكم</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('accounting.accounts') }}">الحسابات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('accounting.journal_entries') }}">القيود اليومية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('accounting.transactions') }}">المعاملات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('accounting.bank_reconciliations') }}">التسويات البنكية</a></li>
                        </ul>
                    </li>

                    <!-- العملاء والموردين -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i> العملاء والموردين
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('customers.customers') }}">العملاء</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('suppliers.suppliers') }}">الموردين</a></li>
                        </ul>
                    </li>

                    <!-- المخزون -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i> المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('inventory.items') }}">الأصناف</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.categories') }}">الفئات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.transactions') }}">الحركات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.adjustments') }}">التعديلات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.transfers') }}">النقلات</a></li>
                        </ul>
                    </li>

                    <!-- الرواتب -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="payrollDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-money-bill-wave"></i> الرواتب
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('payroll.employees') }}">الموظفون</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('payroll.departments') }}">الأقسام</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('payroll.positions') }}">الوظائف</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('payroll.allowances') }}">الإعانات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('payroll.deductions') }}">الخصومات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('payroll.leaves') }}">الإجازات</a></li>
                        </ul>
                    </li>

                    <!-- الأصول الثابتة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="assetsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building"></i> الأصول الثابتة
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('assets.assets') }}">الأصول</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('assets.categories') }}">الفئات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('assets.vendors') }}">الموردين</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('assets.depreciations') }}">الإهلاك</a></li>
                        </ul>
                    </li>

                    <!-- التقارير -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('reports.balance_sheet') }}">الميزانية العمومية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.income_statement') }}">قائمة الدخل</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.cash_flow_statement') }}">قائمة التدفقات النقدية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.trial_balance') }}">قائمة المركز المالي</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.budgets') }}">الميزانيات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.budget_analysis') }}">تحليل الميزانية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.tax_reports') }}">التقارير الضريبية</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> {{ current_user.first_name }} {{ current_user.last_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">إنشاء حساب</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© {{ now.year }} نظام المحاسبة. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
