
{% extends "layout.html" %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>لوحة التحكم</h2>
        <hr>
    </div>
</div>

<div class="row">
    <!-- إحصائيات عامة -->
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_accounts }}</h4>
                        <p class="card-text">إجمالي الحسابات</p>
                    </div>
                    <div>
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_journal_entries }}</h4>
                        <p class="card-text">إجمالي القيود اليومية</p>
                    </div>
                    <div>
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_transactions }}</h4>
                        <p class="card-text">إجمالي المعاملات</p>
                    </div>
                    <div>
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_revenue|default(0)|round(2) }}</h4>
                        <p class="card-text">إجمالي الإيرادات</p>
                    </div>
                    <div>
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المعاملات الأخيرة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">المعاملات الأخيرة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in stats.recent_transactions[:5] %}
                            <tr>
                                <td>{{ transaction.reference_number }}</td>
                                <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ transaction.description }}</td>
                                <td>{{ transaction.amount|round(2) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if transaction.status == 'posted' else 'warning' }}">
                                        {{ transaction.status }}
                                    </span>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد معاملات مسجلة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- القيود اليومية الأخيرة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">القيود اليومية الأخيرة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                                <th>النوع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in stats.recent_journal_entries[:5] %}
                            <tr>
                                <td>{{ entry.reference_number }}</td>
                                <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ entry.account.name }}</td>
                                <td>{{ entry.amount|round(2) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if entry.is_debit else 'success' }}">
                                        {{ 'مدين' if entry.is_debit else 'دائن' }}
                                    </span>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد قيود يومية مسجلة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المخططات المالية -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">نظرة عامة على الأداء المالي</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-success text-white mb-3">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي الإيرادات</h5>
                                <h2>{{ stats.total_revenue|default(0)|round(2) }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white mb-3">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي المصروفات</h5>
                                <h2>{{ stats.total_expenses|default(0)|round(2) }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-primary text-white mb-3">
                            <div class="card-body">
                                <h5 class="card-title">صافي الربح</h5>
                                <h2>{{ stats.net_profit|default(0)|round(2) }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
