# general_ledger.py
import sqlite3

def create_entry(date, description, amount, type_):
    if not all([date, description, amount, type_]):
        raise ValueError("All fields are required")
    if type_ not in ['debit', 'credit']:
        raise ValueError("Type must be either 'debit' or 'credit'")
    if not isinstance(amount, (int, float)):
        raise ValueError("Amount must be a number")
    
    try:
        with sqlite3.connect("accounting.db") as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO ledger (date, description, amount, type) VALUES (?, ?, ?, ?)",
                           (date, description, amount, type_))
            conn.commit()
            return True
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    SOFTWARE = "برمجيات"
    HARDWARE = "أجهزة"    
    OFFICE_SUPPLIES = "لوازم مكتبية"
    MACHINERY = "آلات"
    TOOLS = "أدوات"