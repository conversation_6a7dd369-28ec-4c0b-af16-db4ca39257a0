
# نظام المحاسبة

نظام محاسبي متكامل لإدارة العمليات المالية والمحاسبية في المؤسسات.

## الميزات

### 1. إدارة الحسابات العامة
- دفتر الأستاذ العام
- تسجيل القيود اليومية
- إجراء التسويات البنكية
- إعداد القوائم المالية (الميزانية العمومية، قائمة الدخل، قائمة التدفقات النقدية)
- إغلاق الفترات المحاسبية

### 2. إدارة العملاء والموردين
- متابعة الذمم المدينة (العملاء)
- متابعة الذمم الدائنة (الموردين)
- إصدار الفواتير وإشعارات الدفع
- تتبع المدفوعات والمستحقات
- إنشاء تقارير تفصيلية عن الحسابات المدينة والدائنة
- إدارة عروض الأسعار والطلبات

### 3. المخزون والمشتريات
- مراقبة حركة الأصناف (وارد، صادر، رصيد)
- إدارة المخزون وتحديد نقاط إعادة الطلب
- تسجيل المشتريات وربطها بالموردين
- إنشاء أوامر الشراء والمطالبات
- تقييم المخزون (متوسط التكلفة، FIFO، LIFO)
- تقييم الأصناف التالفة والقديمة

### 4. الرواتب وشؤون الموظفين
- حساب الرواتب الأساسية والمتغيرة
- حساب الضرائب والتأمينات الاجتماعية
- إدارة المكافآت والخصومات
- حساب الإجازات والمغادرات
- توليد كشوف الرواتب تلقائيًا
- إعداد تقارير الرواتب للجهات الرقابية

### 5. إدارة الأصول الثابتة
- تسجيل الأصول الثابتة وتصنيفها
- حساب الإهلاك (الخطي، المتناقص، وحدات الإنتاج)
- تتبع حالة الأصل من الشراء حتى التخلص منه
- إجراء جرد للأصول الثابتة
- تسجيل بيع الأصول أو التخلص منها

### 6. التقارير والتحليلات المالية
- إنشاء تقارير مالية دورية
- تحليل الأداء المالي (نسب السيولة، الربحية، الكفاءة)
- تحليل التدفقات النقدية
- إنشاء ميزانيات تقديرية
- مقارنة الأداء الفعلي بالمخطط
- توفير مؤشرات أداء رئيسية (KPIs)

## المتطلبات التقنية

- Python 3.6 أو أحدث
- Flask
- SQLAlchemy
- Flask-Login
- Flask-Migrate
- Flask-WTF
- Pandas
- ReportLab

## التثبيت

1. استنسخ المستودع:
   ```
   git clone <repository-url>
   cd accounting_system
   ```

2. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   source venv/bin/activate  # على Windows: venv\Scriptsctivate
   ```

3. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```

4. قم بتشغيل التطبيق:
   ```
   python app.py
   ```

## الاستخدام

1. قم بزيارة http://127.0.0.1:5000 في متصفح الويب الخاص بك.
2. قم بإنشاء حساب جديد أو تسجيل الدخول إذا كان لديك حساب بالفعل.
3. ابدأ باستخدام النظام من خلال القوائم المتوفرة في شريط التنقل.

## الترخيص

هذا المشروع مرخص بموجب ترخيص MIT.

## المساهمون

- [اسم المساهم] - [البريد الإلكتروني] - [مساهمات أساسية]

## الإشادات

شكرًا لجميع المساهمين الذين ساهموا في تطوير هذا المشروع.
