from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os
from decimal import Decimal

# استيراد الإعدادات
from config import Config

# تهيئة التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# إنشاء المجلدات الضرورية إذا لم تكن موجودة
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
os.makedirs(app.config['BACKUP_FOLDER'], exist_ok=True)

# تهيئة قواعد البيانات والمكونات الإضافية
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.init_app(app)

# استيراد النماذج والمسارات بعد تعريفها لمنع مشاكل التهيئة
# استيراد النماذج من ملفاتها الخاصة
from models.user import init_user_model, user_loader, User
from models.accounting import init_accounting_models
from models.payroll import init_payroll_models

# تهيئة نموذج المستخدم بعد تعريف db و login_manager
init_user_model(db, login_manager)
login_manager.user_loader(user_loader)

# تهيئة نماذج المحاسبة والرواتب
init_accounting_models(db)
init_payroll_models(db)

# تسجيل المسارات (سيتم إضافتها لاحقاً)
# app.register_blueprint(auth_routes.auth_bp, url_prefix='/auth')
# app.register_blueprint(accounting_routes.accounting_bp, url_prefix='/accounting')
# app.register_blueprint(customer_routes.customer_bp, url_prefix='/customers')
# app.register_blueprint(supplier_routes.supplier_bp, url_prefix='/suppliers')
# app.register_blueprint(inventory_routes.inventory_bp, url_prefix='/inventory')
# app.register_blueprint(payroll_routes.payroll_bp, url_prefix='/payroll')
# app.register_blueprint(asset_routes.asset_bp, url_prefix='/assets')
# app.register_blueprint(report_routes.report_bp, url_prefix='/reports')

# صفحة رئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    # جمع إحصائيات عامة للعرض في لوحة التحكم
    stats = {
        'total_revenue': 0,
        'total_assets': 0,
        'total_expenses': 0,
        'net_profit': 0,
        'total_customers': 0,
        'total_suppliers': 0,
        'recent_transactions': [],
        'recent_invoices': [],
        'recent_payments': []
    }
    return render_template('dashboard.html', stats=stats)

# وظائف مساعدة
def get_total_revenue():
    # حساب إجمالي الإيرادات
    from models.accounting import Account
    revenue_accounts = Account.query.filter(Account.account_type == 'revenue').all()
    total = Decimal(0)
    for account in revenue_accounts:
        total += account.get_balance()
    return total

def get_total_expenses():
    # حساب إجمالي المصروفات
    from models.accounting import Account
    expense_accounts = Account.query.filter(Account.account_type == 'expense').all()
    total = Decimal(0)
    for account in expense_accounts:
        total += account.get_balance()
    return total

def get_net_profit():
    # حساب صافي الربح
    return get_total_revenue() - get_total_expenses()

# نقطة الدخول للتطبيق
if __name__ == '__main__':
    app.run(debug=True)
