from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os
from decimal import Decimal

# استيراد الإعدادات
from config import Config

# تهيئة التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# إنشاء المجلدات الضرورية إذا لم تكن موجودة
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
os.makedirs(app.config['BACKUP_FOLDER'], exist_ok=True)

# تهيئة قواعد البيانات والمكونات الإضافية
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.init_app(app)

# استيراد النماذج والمسارات بعد تعريفها لمنع مشاكل التهيئة
# استيراد النماذج من ملفاتها الخاصة
from models.user import init_user_model, user_loader
from models.accounting import init_accounting_models
from models.customer_supplier import Customer, Supplier, Invoice, PurchaseOrder
from models.inventory import InventoryItem, InventoryTransaction
from models.payroll import Employee, Payroll
from models.assets import Asset, Depreciation
from models.reports import FinancialReport
from models.user import User
from routes import (auth_routes, accounting_routes, customer_routes, supplier_routes,
                   inventory_routes, payroll_routes, asset_routes, report_routes)

# تهيئة نموذج المستخدم بعد تعريف db و login_manager
init_user_model(db, login_manager)
login_manager.user_loader(user_loader)

# تسجيل المسارات
app.register_blueprint(auth_routes.auth_bp, url_prefix='/auth')
app.register_blueprint(accounting_routes.accounting_bp, url_prefix='/accounting')
app.register_blueprint(customer_routes.customer_bp, url_prefix='/customers')
app.register_blueprint(supplier_routes.supplier_bp, url_prefix='/suppliers')
app.register_blueprint(inventory_routes.inventory_bp, url_prefix='/inventory')
app.register_blueprint(payroll_routes.payroll_bp, url_prefix='/payroll')
app.register_blueprint(asset_routes.asset_bp, url_prefix='/assets')
app.register_blueprint(report_routes.report_bp, url_prefix='/reports')

# صفحة رئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    # جمع إحصائيات عامة للعرض في لوحة التحكم
    from models.accounting import Transaction, Payment, Account

    stats = {
        'total_revenue': get_total_revenue(),
        'total_assets': Asset.query.count(),
        'total_expenses': get_total_expenses(),
        'net_profit': get_net_profit(),
        'total_customers': Customer.query.count(),
        'total_suppliers': Supplier.query.count(),
        'recent_transactions': Transaction.query.order_by(Transaction.date.desc()).limit(5).all(),
        'recent_invoices': Invoice.query.order_by(Invoice.date.desc()).limit(5).all(),
        'recent_payments': Payment.query.order_by(Payment.date.desc()).limit(5).all()
    }
    return render_template('dashboard.html', stats=stats)

# وظائف مساعدة
def get_total_revenue():
    # حساب إجمالي الإيرادات
    from models.accounting import Account
    revenue_accounts = Account.query.filter(Account.account_type == 'revenue').all()
    total = Decimal(0)
    for account in revenue_accounts:
        total += account.get_balance()
    return total

def get_total_expenses():
    # حساب إجمالي المصروفات
    from models.accounting import Account
    expense_accounts = Account.query.filter(Account.account_type == 'expense').all()
    total = Decimal(0)
    for account in expense_accounts:
        total += account.get_balance()
    return total

def get_net_profit():
    # حساب صافي الربح
    return get_total_revenue() - get_total_expenses()

# نقطة الدخول للتطبيق
if __name__ == '__main__':
    app.run(debug=True)
