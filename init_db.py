#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تهيئة قاعدة البيانات
Database Initialization Script
"""

from app import app, db
from models.user import init_user_model, User
from models.accounting import init_accounting_models
from werkzeug.security import generate_password_hash
import os

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    with app.app_context():
        try:
            # إنشاء المجلدات المطلوبة
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
            os.makedirs(app.config['BACKUP_FOLDER'], exist_ok=True)
            
            # حذف قاعدة البيانات الموجودة إذا كانت موجودة (للاختبار فقط)
            db.drop_all()
            
            # إنشاء جميع الجداول
            db.create_all()
            
            # إنشاء مستخدم افتراضي
            create_default_user()
            
            # إنشاء حسابات افتراضية
            create_default_accounts()
            
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
            raise

def create_default_user():
    """إنشاء مستخدم افتراضي"""
    try:
        # التحقق من عدم وجود المستخدم
        existing_user = User.query.filter_by(username='admin').first()
        if existing_user:
            print("المستخدم الافتراضي موجود بالفعل")
            return
            
        # إنشاء مستخدم جديد
        admin_user = User()
        admin_user.username = 'admin'
        admin_user.email = '<EMAIL>'
        admin_user.first_name = 'مدير'
        admin_user.last_name = 'النظام'
        admin_user.set_password('admin123')
        admin_user.is_admin = True
        admin_user.is_active = True
        
        db.session.add(admin_user)
        db.session.commit()
        
        print("✅ تم إنشاء المستخدم الافتراضي:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الافتراضي: {str(e)}")
        db.session.rollback()
        raise

def create_default_accounts():
    """إنشاء الحسابات الافتراضية"""
    try:
        from models.accounting import Account, AccountType
        
        # الحسابات الرئيسية
        default_accounts = [
            # الأصول
            {'number': '1000', 'name': 'الأصول', 'type': AccountType.ASSET, 'parent': None},
            {'number': '1100', 'name': 'الأصول المتداولة', 'type': AccountType.ASSET, 'parent': '1000'},
            {'number': '1110', 'name': 'النقدية', 'type': AccountType.ASSET, 'parent': '1100'},
            {'number': '1120', 'name': 'البنك', 'type': AccountType.ASSET, 'parent': '1100'},
            {'number': '1130', 'name': 'العملاء', 'type': AccountType.ASSET, 'parent': '1100'},
            
            # الخصوم
            {'number': '2000', 'name': 'الخصوم', 'type': AccountType.LIABILITY, 'parent': None},
            {'number': '2100', 'name': 'الخصوم المتداولة', 'type': AccountType.LIABILITY, 'parent': '2000'},
            {'number': '2110', 'name': 'الموردون', 'type': AccountType.LIABILITY, 'parent': '2100'},
            
            # حقوق الملكية
            {'number': '3000', 'name': 'حقوق الملكية', 'type': AccountType.EQUITY, 'parent': None},
            {'number': '3100', 'name': 'رأس المال', 'type': AccountType.EQUITY, 'parent': '3000'},
            
            # الإيرادات
            {'number': '4000', 'name': 'الإيرادات', 'type': AccountType.REVENUE, 'parent': None},
            {'number': '4100', 'name': 'إيرادات المبيعات', 'type': AccountType.REVENUE, 'parent': '4000'},
            
            # المصروفات
            {'number': '5000', 'name': 'المصروفات', 'type': AccountType.EXPENSE, 'parent': None},
            {'number': '5100', 'name': 'مصروفات التشغيل', 'type': AccountType.EXPENSE, 'parent': '5000'},
        ]
        
        # إنشاء الحسابات
        created_accounts = {}
        
        for account_data in default_accounts:
            # التحقق من عدم وجود الحساب
            existing = Account.query.filter_by(account_number=account_data['number']).first()
            if existing:
                created_accounts[account_data['number']] = existing
                continue
                
            # إنشاء حساب جديد
            account = Account()
            account.account_number = account_data['number']
            account.name = account_data['name']
            account.account_type = account_data['type']
            
            # تحديد الحساب الأب
            if account_data['parent']:
                parent_account = created_accounts.get(account_data['parent'])
                if parent_account:
                    account.parent_id = parent_account.id
            
            db.session.add(account)
            created_accounts[account_data['number']] = account
        
        db.session.commit()
        print("✅ تم إنشاء الحسابات الافتراضية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات الافتراضية: {str(e)}")
        db.session.rollback()
        raise

if __name__ == '__main__':
    create_database()
