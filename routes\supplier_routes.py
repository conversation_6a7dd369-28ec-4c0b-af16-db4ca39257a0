
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from models import Supplier, SupplierInvoice, SupplierInvoiceItem, SupplierPayment, PurchaseOrder, PurchaseOrderItem
from decimal import Decimal
from datetime import datetime

supplier_bp = Blueprint('suppliers', __name__)

@supplier_bp.route('/')
@login_required
def suppliers():
    # الحصول على جميع الموردين
    suppliers = Supplier.query.all()
    return render_template('suppliers/suppliers.html', suppliers=suppliers)

@supplier_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_supplier():
    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        code = request.form.get('code')
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        tax_id = request.form.get('tax_id')

        # التحقق من عدم تكرار كود المورد
        existing_supplier = Supplier.query.filter_by(code=code).first()
        if existing_supplier:
            flash('كود المورد موجود بالفعل', 'danger')
            return render_template('suppliers/new_supplier.html')

        # إنشاء المورد الجديد
        new_supplier = Supplier(
            code=code,
            name=name,
            contact_person=contact_person,
            email=email,
            phone=phone,
            address=address,
            tax_id=tax_id
        )

        db.session.add(new_supplier)
        db.session.commit()

        flash('تم إنشاء المورد بنجاح', 'success')
        return redirect(url_for('suppliers.suppliers'))

    return render_template('suppliers/new_supplier.html')

@supplier_bp.route('/<supplier_id>')
@login_required
def supplier_detail(supplier_id):
    # الحصول على تفاصيل المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على الفواتير والمدفوعات المرتبطة بالمورد
    invoices = SupplierInvoice.query.filter_by(supplier_id=supplier_id).order_by(SupplierInvoice.date.desc()).all()
    payments = SupplierPayment.query.filter_by(supplier_id=supplier_id).order_by(SupplierPayment.date.desc()).all()
    purchase_orders = PurchaseOrder.query.filter_by(supplier_id=supplier_id).order_by(PurchaseOrder.date.desc()).all()

    # حساب إجمالي المبالغ
    total_invoices = sum(inv.total_amount for inv in invoices if inv.status == 'open')
    total_payments = sum(pay.amount for pay in payments)
    balance = total_invoices - total_payments

    return render_template('suppliers/supplier_detail.html', supplier=supplier, invoices=invoices, payments=payments, purchase_orders=purchase_orders, balance=balance)

@supplier_bp.route('/<supplier_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_supplier(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    if request.method == 'POST':
        # تحديث بيانات المورد
        supplier.code = request.form.get('code')
        supplier.name = request.form.get('name')
        supplier.contact_person = request.form.get('contact_person')
        supplier.email = request.form.get('email')
        supplier.phone = request.form.get('phone')
        supplier.address = request.form.get('address')
        supplier.tax_id = request.form.get('tax_id')

        db.session.commit()

        flash('تم تحديث بيانات المورد بنجاح', 'success')
        return redirect(url_for('suppliers.supplier_detail', supplier_id=supplier_id))

    return render_template('suppliers/edit_supplier.html', supplier=supplier)

@supplier_bp.route('/<supplier_id>/delete', methods=['POST'])
@login_required
def delete_supplier(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # التحقق من وجود فواتير أو مدفوعات أو أوامر شراء مرتبطة بالمورد
    if supplier.invoices.count() > 0 or supplier.payments.count() > 0 or supplier.purchase_orders.count() > 0:
        flash('لا يمكن حذف المورد لأن لديه فواتير أو مدفوعات أو أوامر شراء مسجلة', 'danger')
        return redirect(url_for('suppliers.supplier_detail', supplier_id=supplier_id)

    db.session.delete(supplier)
    db.session.commit()

    flash('تم حذف المورد بنجاح', 'success')
    return redirect(url_for('suppliers.suppliers'))

@supplier_bp.route('/<supplier_id>/invoices')
@login_required
def supplier_invoices(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على جميع فواتير المورد
    invoices = SupplierInvoice.query.filter_by(supplier_id=supplier_id).order_by(SupplierInvoice.date.desc()).all()

    return render_template('supplier_invoices.html', supplier=supplier, invoices=invoices)

@supplier_bp.route('/<supplier_id>/new-invoice', methods=['GET', 'POST'])
@login_required
def new_supplier_invoice(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        due_date = datetime.strptime(request.form.get('due_date'), '%Y-%m-%d').date()

        # إنشاء الفاتورة
        invoice = SupplierInvoice(
            supplier_id=supplier_id,
            date=date,
            due_date=due_date,
            total_amount=Decimal('0.00'),  # سيتم تحديثه لاحقًا
            tax_amount=Decimal('0.00'),
            discount_amount=Decimal('0.00'),
            status='draft'
        )
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID الفاتورة

        # جمع بنود الفاتورة
        total_amount = Decimal('0.00')
        tax_amount = Decimal('0.00')
        discount_amount = Decimal(request.form.get('discount_amount', 0))

        for key, value in request.form.items():
            if key.startswith('item_description_'):
                index = key.split('_')[2]
                description = value
                quantity = Decimal(request.form.get(f'item_quantity_{index}', 0))
                unit_price = Decimal(request.form.get(f'item_unit_price_{index}', 0))
                tax_rate = Decimal(request.form.get(f'item_tax_rate_{index}', 0))

                # حساب إجمالي البند
                line_total = quantity * unit_price
                line_tax = line_total * (tax_rate / 100)

                # إنشاء بند الفاتورة
                invoice_item = SupplierInvoiceItem(
                    invoice_id=invoice.id,
                    description=description,
                    quantity=quantity,
                    unit_price=unit_price,
                    tax_rate=tax_rate
                )
                db.session.add(invoice_item)

                # تحديث المجاميع
                total_amount += line_total
                tax_amount += line_tax

        # تطبيق الخصم
        if discount_amount > 0:
            # إذا كان الخصم نسبة مئوية
            if discount_amount <= 100:
                discount_amount = total_amount * (discount_amount / 100)
            total_amount -= discount_amount

        # تحديث الفاتورة بالمجاميع النهائية
        invoice.total_amount = total_amount
        invoice.tax_amount = tax_amount
        invoice.discount_amount = discount_amount
        db.session.commit()

        flash('تم إنشاء الفاتورة بنجاح', 'success')
        return redirect(url_for('suppliers.supplier_invoices', supplier_id=supplier_id)

    return render_template('suppliers/new_invoice.html', supplier=supplier)

@supplier_bp.route('/<supplier_id>/payments')
@login_required
def supplier_payments(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على جميع المدفوعات المرتبطة بالمورد
    payments = SupplierPayment.query.filter_by(supplier_id=supplier_id).order_by(SupplierPayment.date.desc()).all()

    return render_template('supplier_payments.html', supplier=supplier, payments=payments)

@supplier_bp.route('/<supplier_id>/new-payment', methods=['GET', 'POST'])
@login_required
def new_supplier_payment(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على الفواتير المفتوحة للمورد
    open_invoices = SupplierInvoice.query.filter_by(supplier_id=supplier_id, status='open').all()

    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        amount = Decimal(request.form.get('amount'))
        payment_method = request.form.get('payment_method')
        reference_number = request.form.get('reference_number')
        notes = request.form.get('notes')

        # إنشاء المدفوعة
        payment = SupplierPayment(
            supplier_id=supplier_id,
            date=date,
            amount=amount,
            payment_method=payment_method,
            reference_number=reference_number,
            notes=notes
        )
        db.session.add(payment)
        db.session.flush()  # للحصول على ID المدفوعة

        # تحديث الفاتورة المدفوعة إذا تم تحديد فاتورة محددة
        invoice_id = request.form.get('invoice_id')
        if invoice_id:
            invoice = SupplierInvoice.query.get(invoice_id)
            if invoice:
                # تحديث المبلغ المتبقي للفاتورة
                remaining_amount = invoice.get_remaining_amount()
                payment_amount = min(amount, remaining_amount)

                # تحديث المدفوعة بالفعل
                payment.invoice_id = invoice.id
                payment.amount = payment_amount

                # تحديث حالة الفاتورة إذا تم دفع كامل المبلغ
                if remaining_amount - payment_amount <= 0:
                    invoice.status = 'paid'

                amount -= payment_amount

                # إذا كان هناك مبلغ إضافي، يمكن استخدامه لفاتورة أخرى أو إرجاعه للمورد
                if amount > 0:
                    flash(f'تم دفع {payment_amount} للفاتورة {invoice.invoice_number}، و {amount} متبقي', 'info')

        db.session.commit()

        flash('تم تسجيل المدفوعة بنجاح', 'success')
        return redirect(url_for('suppliers.supplier_payments', supplier_id=supplier_id)

    return render_template('suppliers/new_payment.html', supplier=supplier, open_invoices=open_invoices)

@supplier_bp.route('/<supplier_id>/purchase-orders')
@login_required
def supplier_purchase_orders(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على جميع أوامر الشراء المرتبطة بالمورد
    purchase_orders = PurchaseOrder.query.filter_by(supplier_id=supplier_id).order_by(PurchaseOrder.date.desc()).all()

    return render_template('supplier_purchase_orders.html', supplier=supplier, purchase_orders=purchase_orders)

@supplier_bp.route('/<supplier_id>/new-purchase-order', methods=['GET', 'POST'])
@login_required
def new_supplier_purchase_order(supplier_id):
    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    if request.method == 'POST':
        # جمع البيانات من نموذج الإدخال
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        expected_date = datetime.strptime(request.form.get('expected_date'), '%Y-%m-%d').date()

        # إنشاء أمر الشراء
        purchase_order = PurchaseOrder(
            supplier_id=supplier_id,
            date=date,
            expected_date=expected_date,
            status='draft'
        )
        db.session.add(purchase_order)
        db.session.flush()  # للحصول على ID أمر الشراء

        # جمع بنود أمر الشراء
        for key, value in request.form.items():
            if key.startswith('item_description_'):
                index = key.split('_')[2]
                description = value
                quantity = Decimal(request.form.get(f'item_quantity_{index}', 0))
                unit_price = Decimal(request.form.get(f'item_unit_price_{index}', 0))

                # إنشاء بند أمر الشراء
                purchase_order_item = PurchaseOrderItem(
                    purchase_order_id=purchase_order.id,
                    description=description,
                    quantity=quantity,
                    unit_price=unit_price
                )
                db.session.add(purchase_order_item)

        db.session.commit()

        flash('تم إنشاء أمر الشراء بنجاح', 'success')
        return redirect(url_for('suppliers.supplier_purchase_orders', supplier_id=supplier_id)

    return render_template('suppliers/new_purchase_order.html', supplier=supplier)
