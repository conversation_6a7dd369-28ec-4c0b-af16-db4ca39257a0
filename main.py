# main.py
import tkinter as tk
from tkinter import ttk, messagebox
from modules.general_ledger import create_entry
from modules.clients_suppliers import issue_invoice
from database.models import init_db

class AccountingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام المحاسبة")
        self.root.geometry("400x500")
        
        # إطار القيد المحاسبي
        entry_frame = ttk.LabelFrame(root, text="تسجيل قيد محاسبي", padding=10)
        entry_frame.pack(padx=10, pady=10, fill="x")
        
        # حقول إدخال القيد المحاسبي
        ttk.Label(entry_frame, text="التاريخ:").grid(row=0, column=0, sticky="w")
        self.date_entry = ttk.Entry(entry_frame)
        self.date_entry.grid(row=0, column=1, padx=5, pady=5)
        self.date_entry.insert(0, "2025-09-22")
        
        ttk.Label(entry_frame, text="الوصف:").grid(row=1, column=0, sticky="w")
        self.desc_entry = ttk.Entry(entry_frame)
        self.desc_entry.grid(row=1, column=1, padx=5, pady=5)
        self.desc_entry.insert(0, "مبيعات")
        
        ttk.Label(entry_frame, text="المبلغ:").grid(row=2, column=0, sticky="w")
        self.amount_entry = ttk.Entry(entry_frame)
        self.amount_entry.grid(row=2, column=1, padx=5, pady=5)
        self.amount_entry.insert(0, "5000")
        
        ttk.Label(entry_frame, text="النوع:").grid(row=3, column=0, sticky="w")
        self.type_var = tk.StringVar(value="مدين")
        type_combo = ttk.Combobox(entry_frame, textvariable=self.type_var, values=["مدين", "دائن"])
        type_combo.grid(row=3, column=1, padx=5, pady=5)
        
        btn_entry = ttk.Button(entry_frame, text="تسجيل القيد", command=self.show_create_entry)
        btn_entry.grid(row=4, column=0, columnspan=2, pady=10)
        
        # إطار الفاتورة
        invoice_frame = ttk.LabelFrame(root, text="إصدار فاتورة", padding=10)
        invoice_frame.pack(padx=10, pady=10, fill="x")
        
        # حقول إدخال الفاتورة
        ttk.Label(invoice_frame, text="اسم العميل:").grid(row=0, column=0, sticky="w")
        self.client_entry = ttk.Entry(invoice_frame)
        self.client_entry.grid(row=0, column=1, padx=5, pady=5)
        self.client_entry.insert(0, "شركة النور")
        
        ttk.Label(invoice_frame, text="المبلغ:").grid(row=1, column=0, sticky="w")
        self.invoice_amount_entry = ttk.Entry(invoice_frame)
        self.invoice_amount_entry.grid(row=1, column=1, padx=5, pady=5)
        self.invoice_amount_entry.insert(0, "3200")
        
        btn_invoice = ttk.Button(invoice_frame, text="إصدار الفاتورة", command=self.show_issue_invoice)
        btn_invoice.grid(row=2, column=0, columnspan=2, pady=10)
        
        # تهيئة قاعدة البيانات مرة واحدة فقط
        try:
            init_db()
            messagebox.showinfo("نجاح", "تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تهيئة قاعدة البيانات: {str(e)}")

    def show_create_entry(self):
        try:
            date = self.date_entry.get()
            desc = self.desc_entry.get()
            amount = float(self.amount_entry.get())
            entry_type = self.type_var.get()
            
            create_entry(date, desc, amount, entry_type)
            messagebox.showinfo("تم", "تم تسجيل القيد بنجاح")
        except ValueError:
            messagebox.showerror("خطأ", "الرجاء إدخال قيمة رقمية صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_issue_invoice(self):
        try:
            client = self.client_entry.get()
            amount = float(self.invoice_amount_entry.get())
            
            issue_invoice(client, amount)
            messagebox.showinfo("تم", "تم إصدار الفاتورة بنجاح")
        except ValueError:
            messagebox.showerror("خطأ", "الرجاء إدخال قيمة رقمية صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = AccountingApp(root)
    root.mainloop()
