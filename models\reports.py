
from app import db
from datetime import datetime, date
from decimal import Decimal
import uuid

class FinancialReport(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    report_type = db.Column(db.String(50), nullable=False)  # balance_sheet, income_statement, cash_flow
    report_name = db.Column(db.String(100), nullable=False)
    report_date = db.Column(db.Date, nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)  # 1-12 for monthly, 1-4 for quarterly
    fiscal_quarter = db.Column(db.Integer, nullable=True)  # 1-4 for quarterly reports
    generated_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    is_draft = db.Column(db.<PERSON>, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    generated_by = db.relationship('User')
    report_sections = db.relationship('ReportSection', backref='financial_report', lazy='dynamic')

    def __repr__(self):
        return f'<FinancialReport {self.report_name}>'

class ReportSection(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    financial_report_id = db.Column(db.String(36), db.ForeignKey('financial_report.id'), nullable=False)
    section_name = db.Column(db.String(100), nullable=False)
    section_order = db.Column(db.Integer, nullable=False)
    is_summary = db.Column(db.Boolean, default=False)
    content = db.Column(db.Text)  # JSON content for the section
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    financial_report = db.relationship('FinancialReport')

    def __repr__(self):
        return f'<ReportSection {self.section_name}>'

class BalanceSheet(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    report_date = db.Column(db.Date, nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)

    # الأصول
    total_current_assets = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    cash_and_equivalents = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    accounts_receivable = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    inventory = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    prepaid_expenses = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    total_non_current_assets = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    property_plant_equipment = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    intangible_assets = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    investments = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    total_assets = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # الالتزامات والحقوق
    total_current_liabilities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    accounts_payable = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    short_term_loans = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    current_portion_of_long_term_debt = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    total_non_current_liabilities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    long_term_loans = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    deferred_tax_liabilities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    total_liabilities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # حقوق الملكية
    share_capital = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    retained_earnings = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    total_equity = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<BalanceSheet {self.report_date}>'

class IncomeStatement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    report_date = db.Column(db.Date, nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)

    # الإيرادات
    total_revenue = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    sales_revenue = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    service_revenue = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    other_revenue = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # التكاليف
    cost_of_sales = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    gross_profit = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # المصروفات التشغيلية
    total_operating_expenses = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    selling_expenses = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    administrative_expenses = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    depreciation_expense = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    amortization_expense = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # الدخل من العمليات
    operating_income = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # الدخل غير التشغيلي
    interest_income = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    interest_expense = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    other_income = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    other_expenses = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # الضرائب
    income_tax_expense = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # الدخل الصافي
    net_income = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<IncomeStatement {self.report_date}>'

class CashFlowStatement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    report_date = db.Column(db.Date, nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)

    # التدفقات النقدية من العمليات
    cash_from_operating_activities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    net_income = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    depreciation_and_amortization = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    changes_in_working_capital = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # التدفقات النقدية من الاستثمار
    cash_from_investing_activities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    capital_expenditures = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    proceeds_from_asset_sales = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    acquisitions = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # التدفقات النقدية من التمويل
    cash_from_financing_activities = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    proceeds_from_debt = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    debt_repayments = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    proceeds_from_equity = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    dividends_paid = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    # التغير في النقد
    net_cash_flow = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    cash_at_beginning = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))
    cash_at_end = db.Column(db.Numeric(20, 2), default=Decimal('0.00'))

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<CashFlowStatement {self.report_date}>'

class Budget(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    fiscal_year = db.Column(db.Integer, nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='draft')  # draft, active, closed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User')
    budget_items = db.relationship('BudgetItem', backref='budget', lazy='dynamic')

    def __repr__(self):
        return f'<Budget {self.name}>'

class BudgetItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    budget_id = db.Column(db.Integer, db.ForeignKey('budget.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False)
    period = db.Column(db.Integer, nullable=False)  # 1-12 for monthly
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    actual_amount = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    variance = db.Column(db.Numeric(15, 2), default=Decimal('0.00'))
    variance_percent = db.Column(db.Numeric(10, 2), default=Decimal('0.00'))
    notes = db.Column(db.Text)

    # العلاقات
    account = db.relationship('Account')

    def __repr__(self):
        return f'<BudgetItem {self.id}>'

class TaxReport(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    tax_type = db.Column(db.String(50), nullable=False)  # income_tax, sales_tax, payroll_tax
    report_period = db.Column(db.String(20), nullable=False)  # monthly, quarterly, annually
    fiscal_year = db.Column(db.Integer, nullable=False)
    fiscal_period = db.Column(db.Integer, nullable=False)
    tax_amount = db.Column(db.Numeric(15, 2), nullable=False)
    taxable_amount = db.Column(db.Numeric(15, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), nullable=False)
    filing_date = db.Column(db.Date, nullable=True)
    payment_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.String(20), default='pending')  # pending, filed, paid
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User')

    def __repr__(self):
        return f'<TaxReport {self.tax_type}>'
